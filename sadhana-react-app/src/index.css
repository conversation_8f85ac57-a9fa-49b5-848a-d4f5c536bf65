/* Font Face Declarations - Using web-safe fallbacks for now */

/* Typography Classes */
.fs-h1 {
  font-family: Georgia, 'Times New Roman', serif;
  font-size: 40px;
  line-height: 39px;
  letter-spacing: 0.02em;
  color: #51301d;
}

@media (min-width: 768px) {
  .fs-h1 {
    font-size: 48px;
    line-height: 47.2px;
  }
}

@media (min-width: 1025px) {
  .fs-h1 {
    font-size: 64px;
    line-height: 62.4px;
  }
}

@media (min-width: 1441px) {
  .fs-h1 {
    font-size: 80px;
    line-height: 77.6px;
  }
}

.fs-h3 {
  font-family: Georgia, 'Times New Roman', serif;
  font-size: 20px;
  line-height: 20px;
  letter-spacing: 0.065em;
  color: #a77b2a;
  text-transform: uppercase;
}

@media (min-width: 768px) {
  .fs-h3 {
    font-size: 20.8px;
    line-height: 20.8px;
  }
}

@media (min-width: 1025px) {
  .fs-h3 {
    font-size: 27.2px;
    line-height: 27.2px;
  }
}

@media (min-width: 1441px) {
  .fs-h3 {
    font-size: 32px;
    line-height: 32px;
  }
}

.fs-labeur {
  font-family: Arial, sans-serif;
  font-size: 20px;
  line-height: 24px;
  color: #2a221e;
}

@media (min-width: 1025px) {
  .fs-labeur {
    font-size: 20px;
    line-height: 24px;
  }
}

.fs-labeur-little {
  font-family: Arial, sans-serif;
  font-size: 18px;
  line-height: 23px;
  color: #2a221e;
}

@media (min-width: 1025px) {
  .fs-labeur-little {
    font-size: 21.6px;
    line-height: 26px;
  }
}

.fs-menu {
  font-family: Arial, sans-serif;
  font-size: 16px;
  line-height: 16px;
}

@media (min-width: 1025px) {
  .fs-menu {
    font-size: 17.6px;
    line-height: 19.2px;
  }
}

/* Reset and Base Styles */
body {
  margin: 0;
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  opacity: 0;
  transition: opacity 0.3s ease;
}

body.loaded {
  opacity: 1;
}

* {
  box-sizing: border-box;
}

/* Global Loader */
#sadhana-global-loader {
  z-index: 1;
  position: fixed;
  top: 50%;
  left: 50%;
}

#sadhana-global-loader canvas {
  background-color: transparent;
  transform: translate3d(-50%, -50%, 0);
}
