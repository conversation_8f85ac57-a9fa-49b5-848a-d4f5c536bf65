/* Interactive Components Styles */

/* Mobile Navigation Menu */
#menu-mobile {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(81, 48, 29, 0.95);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

#menu-mobile.active {
  opacity: 1;
  visibility: visible;
}

#menu-mobile .content {
  text-align: center;
  color: white;
}

#menu-mobile .links {
  list-style: none;
  margin: 0 0 40px 0;
  padding: 0;
}

#menu-mobile .links li {
  margin: 20px 0;
}

#menu-mobile .links a {
  font-family: Georgia, 'Times New Roman', serif;
  font-size: 48px;
  line-height: 1.2;
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

#menu-mobile .links a:hover {
  color: #a77b2a;
}

#menu-mobile .language-selector {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0 0 40px 0;
  padding: 0;
  gap: 20px;
}

#menu-mobile .language-selector a {
  font-family: Arial, sans-serif;
  font-size: 24px;
  color: white;
  text-decoration: none;
}

/* App Download Popup */
#popin-app {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

#popin-app.active {
  opacity: 1;
  visibility: visible;
}

#popin-app .content {
  position: relative;
  background: white;
  padding: 60px;
  max-width: 600px;
  width: 90%;
  text-align: center;
}

#popin-app .corner {
  position: absolute;
  width: 107px;
  height: 107px;
}

#popin-app .corner.top-left {
  top: 0;
  left: 0;
}

#popin-app .corner.top-right {
  top: 0;
  right: 0;
  transform: rotate(90deg);
}

#popin-app .corner.bottom-right {
  bottom: 0;
  right: 0;
  transform: rotate(180deg);
}

#popin-app .corner.bottom-left {
  bottom: 0;
  left: 0;
  transform: rotate(270deg);
}

#popin-app .emblem {
  width: 38px;
  height: 38px;
  margin-bottom: 30px;
}

#popin-app .close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-family: Arial, sans-serif;
  font-size: 16px;
  color: #51301d;
  cursor: pointer;
  text-transform: uppercase;
}

#popin-app .title {
  font-family: Georgia, 'Times New Roman', serif;
  font-size: 32px;
  color: #51301d;
  margin-bottom: 40px;
}

#popin-app .applications {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

#popin-app .applications .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #51301d;
  transition: transform 0.3s ease;
}

#popin-app .applications .item:hover {
  transform: translateY(-5px);
}

#popin-app .applications .item div {
  margin-bottom: 15px;
}

#popin-app .applications .item img {
  width: 105px;
  height: 105px;
}

#popin-app .applications .item span {
  font-family: Arial, sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Cookie Banner */
#cookie-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(81, 48, 29, 0.1);
  z-index: 1500;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

#cookie-banner.active {
  transform: translateY(0);
}

#cookie-banner .content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 40px;
  gap: 20px;
  flex-wrap: wrap;
}

#cookie-banner span {
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #51301d;
  flex: 1;
  min-width: 200px;
}

#cookie-banner a {
  color: #a77b2a;
  text-decoration: none;
}

#cookie-banner a:hover {
  text-decoration: underline;
}

#cookie-banner .button {
  padding: 8px 16px;
  font-size: 14px;
}

/* Video Player */
#videoplayer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 4000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

#videoplayer.active {
  opacity: 1;
  visibility: visible;
}

#videoplayer .content {
  position: relative;
  width: 90%;
  max-width: 1200px;
  aspect-ratio: 16/9;
}

#videoplayer iframe {
  width: 100%;
  height: 100%;
  border: none;
}

#videoplayer .close {
  position: absolute;
  top: -50px;
  right: 0;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s ease;
}

#videoplayer .close:hover {
  background: rgba(255, 255, 255, 0.3);
}

#videoplayer .close .icon {
  color: white;
  font-size: 20px;
}

/* Footer */
footer {
  background: #51301d;
  color: white;
  padding: 80px 40px 40px;
  text-align: center;
}

footer .content {
  max-width: 1200px;
  margin: 0 auto;
}

footer .emblem {
  width: 219px;
  height: 209px;
  margin-bottom: 40px;
}

footer .get-app {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  font-family: Arial, sans-serif;
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

footer .get-app .icon {
  font-size: 24px;
}

footer .applications {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

footer .applications .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: white;
  transition: transform 0.3s ease;
}

footer .applications .item:hover {
  transform: translateY(-5px);
}

footer .applications .item div {
  margin-bottom: 15px;
}

footer .applications .item img {
  width: 105px;
  height: 105px;
}

footer .applications .item span {
  font-family: Arial, sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.bottom-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}

.bottom-links li {
  display: flex;
  align-items: center;
  gap: 15px;
}

.bottom-links a {
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.bottom-links a:hover {
  color: white;
}

.bottom-links .social {
  display: flex;
  align-items: center;
  gap: 15px;
}

.bottom-links .social span {
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.bottom-links .social .icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.bottom-links .social a:hover .icon {
  color: white;
}

/* Responsive Styles */
@media (max-width: 767px) {
  #popin-app .content {
    padding: 40px 20px;
  }
  
  #popin-app .applications {
    gap: 20px;
  }
  
  #cookie-banner .content {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
    padding: 20px;
  }
  
  footer {
    padding: 60px 20px 20px;
  }
  
  footer .applications {
    gap: 30px;
  }
  
  .bottom-links {
    flex-direction: column;
    gap: 15px;
  }
  
  .bottom-links .social {
    justify-content: center;
  }
}
