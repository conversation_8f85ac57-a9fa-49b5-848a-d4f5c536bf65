/* Homepage and Slides Styles */
.homepage {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.fullsize {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80px;
  box-sizing: border-box;
  transition: transform 0.8s ease, opacity 0.8s ease;
  z-index: 1;
}

.pageslide {
  opacity: 0;
  transform: translateY(100vh);
}

.pageslide.active {
  opacity: 1;
  transform: translateY(0);
  z-index: 10;
}

.pageslide-up {
  transform: translateY(-100vh);
}

.pageslide-down {
  transform: translateY(100vh);
}

/* Individual slide backgrounds - will be set via inline styles */
.slide-1 {
  background-size: cover;
  background-position: center 35%;
}

.slide-2 {
  background-size: cover;
  background-position: center 80%;
}

.slide-3 {
  background-size: cover;
  background-position: center 35%;
}

.slide-4 {
  background-size: cover;
  background-position: center 75%;
}

.slide-5 {
  background-size: cover;
  background-position: center;
  background-color: #51301d;
}

/* Slide Indicators */
.slide-indicators {
  position: fixed;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.slide-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(26, 26, 46, 0.2);
  border: 2px solid rgba(26, 26, 46, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.slide-indicator.active {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-color: #1a1a2e;
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(26, 26, 46, 0.3);
}

.slide-indicator:hover {
  background: rgba(26, 26, 46, 0.4);
  transform: scale(1.1);
}

.layout {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80px;
  box-sizing: border-box;
}

.left-content {
  flex: 1;
  max-width: 500px;
  color: #1a1a2e;
}

.left-content h1 {
  font-family: Georgia, serif;
  font-size: 96px;
  line-height: 96px;
  font-weight: 300;
  margin: 0 0 40px 0;
  color: #1a1a2e;
  letter-spacing: -2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.left-content p {
  font-family: Arial, sans-serif;
  font-size: 20px;
  line-height: 28px;
  margin: 0 0 60px 0;
  color: #4a5568;
  font-weight: 300;
}

.right-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.slide-circle {
  position: relative;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: radial-gradient(circle at center,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 248, 255, 0.8) 30%,
    rgba(230, 240, 255, 0.6) 60%,
    rgba(220, 235, 255, 0.3) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 80px rgba(100, 150, 255, 0.2),
              inset 0 0 50px rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.slide-image {
  width: 250px;
  height: 200px;
  object-fit: contain;
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.15));
}

.slide-navigation {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.nav-arrow {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-arrow:hover {
  background: rgba(255, 255, 255, 0.3);
}

.nav-arrow svg {
  width: 20px;
  height: 20px;
  fill: white;
}

.link {
  position: relative;
  font-family: Arial, sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #51301d;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: color 0.3s ease;
}

.link:hover {
  color: #a77b2a;
}

.link .underline {
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #a77b2a;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.link:hover .underline {
  opacity: 1;
}

/* Slide 2 - Home Slider */
.slide-2 {
  background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.fullheight {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 100px 40px 40px;
  position: relative;
}

.texts-top {
  text-align: center;
  color: white;
}

.texts-top h1 {
  margin: 0 0 20px 0;
  color: white;
}

.texts-top p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
}

.texts-bottom {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 40px;
  flex-wrap: wrap;
}

.slide-text {
  text-align: center;
  max-width: 200px;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.slide-text.active {
  opacity: 1;
}

.slide-title {
  margin: 0 0 10px 0;
  color: white;
}

.slide-caption {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 18px;
}

.slideshow {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.elipse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.items-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.items {
  position: relative;
  width: 300px;
  height: 300px;
}

.slide-picture {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  opacity: 0;
  transition: all 0.5s ease;
}

.slide-picture.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.slide-shadow {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 30px;
  opacity: 0.3;
}

.slide-overflow {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slide-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
}

.slide-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  opacity: 0.5;
  pointer-events: none;
}

.controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.prev, .next {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.prev:hover, .next:hover {
  background: rgba(255, 255, 255, 0.3);
}

.prev .icon {
  transform: rotate(180deg);
}

.icon {
  font-family: 'icons';
  font-size: 16px;
  color: white;
}

/* Footer Slide */
.slide-5 {
  background: #51301d;
}

/* Animation Classes */
.letter {
  display: inline-block;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.row {
  display: block;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

/* Smooth transitions for all interactive elements */
.button,
.link,
.slide-picture,
.slide-text {
  transition: all 0.3s ease;
}

/* Enhanced hover effects */
.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.slide-picture:hover {
  transform: translate(-50%, -50%) scale(1.05);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .texts-bottom {
    gap: 20px;
  }

  .slide-text {
    max-width: 150px;
  }

  .slideshow {
    height: 300px;
  }

  .items {
    width: 250px;
    height: 250px;
  }

  .slide-picture {
    width: 150px;
    height: 150px;
  }

  .fs-h1 {
    font-size: 36px;
    line-height: 38px;
  }

  .fs-labeur {
    font-size: 18px;
    line-height: 22px;
  }
}

@media (max-width: 767px) {
  .layout {
    padding: 0 20px;
  }

  .slide-inner {
    padding: 20px;
  }

  .fullheight {
    padding: 80px 20px 20px;
  }

  .texts-bottom {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .slide-text {
    max-width: 300px;
  }

  .slideshow {
    height: 250px;
  }

  .items {
    width: 200px;
    height: 200px;
  }

  .slide-picture {
    width: 120px;
    height: 120px;
  }

  .elipse.hide-desktop {
    display: block;
  }

  .elipse.hide-mobile {
    display: none;
  }

  .slide-indicators {
    right: 20px;
    gap: 10px;
  }

  .slide-indicator {
    width: 10px;
    height: 10px;
  }
}
