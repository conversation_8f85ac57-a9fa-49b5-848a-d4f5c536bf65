/* Homepage and Slides Styles */
.homepage {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.fullsize {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pageslide {
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.8s ease;
}

.pageslide.active {
  opacity: 1;
  transform: translateX(0);
}

.pageslide-right {
  transform: translateX(100px);
}

.pageslide-left {
  transform: translateX(-100px);
}

.layout {
  width: 100%;
  max-width: 1200px;
  padding: 0 40px;
}

.center-h {
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-inner {
  text-align: center;
  max-width: 800px;
}

.slide-inner h1 {
  margin: 0 0 30px 0;
}

.text-wrapper {
  margin-top: 40px;
}

.text-wrapper p {
  margin: 0 0 30px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.button-wrapper {
  margin-top: 40px;
}

.link {
  position: relative;
  font-family: 'Hind Guntur', <PERSON><PERSON>;
  font-size: 16px;
  font-weight: 600;
  color: #51301d;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: color 0.3s ease;
}

.link:hover {
  color: #a77b2a;
}

.link .underline {
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: url('%PUBLIC_URL%/images/underline.png') repeat-x;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.link:hover .underline {
  opacity: 1;
}

/* Slide 2 - Home Slider */
.slide-2 {
  background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.fullheight {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 100px 40px 40px;
}

.texts-top {
  text-align: center;
  color: white;
}

.texts-top h1 {
  margin: 0 0 20px 0;
  color: white;
}

.texts-top p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
}

.texts-bottom {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 40px;
  flex-wrap: wrap;
}

.slide-text {
  text-align: center;
  max-width: 200px;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.slide-text.active {
  opacity: 1;
}

.slide-title {
  margin: 0 0 10px 0;
  color: white;
}

.slide-caption {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 18px;
}

.slideshow {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.elipse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.items-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.items {
  position: relative;
  width: 300px;
  height: 300px;
}

.slide-picture {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  opacity: 0;
  transition: all 0.5s ease;
}

.slide-picture.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.slide-shadow {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 30px;
  opacity: 0.3;
}

.slide-overflow {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slide-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
}

.slide-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  opacity: 0.5;
  pointer-events: none;
}

.controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.prev, .next {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.prev:hover, .next:hover {
  background: rgba(255, 255, 255, 0.3);
}

.prev .icon {
  transform: rotate(180deg);
}

.icon {
  font-family: 'icons';
  font-size: 16px;
  color: white;
}

/* Footer Slide */
.slide-5 {
  background: #51301d;
}

/* Animation Classes */
.letter {
  display: inline-block;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.row {
  display: block;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

/* Smooth transitions for all interactive elements */
.button,
.link,
.slide-picture,
.slide-text {
  transition: all 0.3s ease;
}

/* Enhanced hover effects */
.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.slide-picture:hover {
  transform: translate(-50%, -50%) scale(1.05);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .texts-bottom {
    gap: 20px;
  }

  .slide-text {
    max-width: 150px;
  }

  .slideshow {
    height: 300px;
  }

  .items {
    width: 250px;
    height: 250px;
  }

  .slide-picture {
    width: 150px;
    height: 150px;
  }

  .fs-h1 {
    font-size: 36px;
    line-height: 38px;
  }

  .fs-labeur {
    font-size: 18px;
    line-height: 22px;
  }
}

@media (max-width: 767px) {
  .layout {
    padding: 0 20px;
  }
  
  .fullheight {
    padding: 80px 20px 20px;
  }
  
  .texts-bottom {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .slide-text {
    max-width: 300px;
  }
  
  .slideshow {
    height: 250px;
  }
  
  .items {
    width: 200px;
    height: 200px;
  }
  
  .slide-picture {
    width: 120px;
    height: 120px;
  }
  
  .elipse.hide-desktop {
    display: block;
  }
  
  .elipse.hide-mobile {
    display: none;
  }
}
