/* Homepage and Slides Styles */
.homepage {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.fullsize {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80px;
  box-sizing: border-box;
  transition: transform 0.8s ease, opacity 0.8s ease;
  z-index: 1;
}

.pageslide {
  opacity: 0;
  transform: translateY(100vh);
}

.pageslide.active {
  opacity: 1;
  transform: translateY(0);
  z-index: 10;
}

.pageslide-up {
  transform: translateY(-100vh);
}

.pageslide-down {
  transform: translateY(100vh);
}

/* Individual slide backgrounds - will be set via inline styles */
.slide-1 {
  background-size: cover;
  background-position: center 35%;
  position: relative;
}

.slide-1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.85) 100%);
  z-index: 1;
}

.slide-1 .layout {
  position: relative;
  z-index: 2;
}

.slide-2 {
  background-size: cover;
  background-position: center 80%;
  position: relative;
}

.slide-2::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(30, 41, 59, 0.85) 50%,
    rgba(51, 65, 85, 0.8) 100%);
  z-index: 1;
}

.slide-2 .fullheight {
  position: relative;
  z-index: 2;
}

.slide-3 {
  background-size: cover;
  background-position: center 35%;
  position: relative;
}

.slide-3::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  z-index: 1;
}

.slide-3 .layout {
  position: relative;
  z-index: 2;
}

.slide-4 {
  background-size: cover;
  background-position: center 75%;
  position: relative;
}

.slide-4::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(241, 245, 249, 0.95) 0%,
    rgba(226, 232, 240, 0.9) 100%);
  z-index: 1;
}

.slide-4 .layout {
  position: relative;
  z-index: 2;
}

.slide-5 {
  background-size: cover;
  background-position: center;
  background-color: #0f172a;
  position: relative;
}

.slide-5::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 100%);
  z-index: 1;
}

.slide-5 .layout {
  position: relative;
  z-index: 2;
}

/* Slide Indicators */
.slide-indicators {
  position: fixed;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.slide-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(26, 26, 46, 0.2);
  border: 2px solid rgba(26, 26, 46, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.slide-indicator.active {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-color: #1a1a2e;
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(26, 26, 46, 0.3);
}

.slide-indicator:hover {
  background: rgba(26, 26, 46, 0.4);
  transform: scale(1.1);
}

.layout {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80px;
  box-sizing: border-box;
}

.left-content {
  flex: 1;
  max-width: 500px;
  color: #1a1a2e;
}

.left-content h1 {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 96px;
  line-height: 88px;
  font-weight: 300;
  margin: 0 0 40px 0;
  color: #1e293b;
  letter-spacing: -3px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.left-content p {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 22px;
  line-height: 32px;
  margin: 0 0 60px 0;
  color: #64748b;
  font-weight: 400;
  letter-spacing: 0.5px;
}

.right-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.slide-circle {
  position: relative;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%,
    rgba(255, 255, 255, 1) 0%,
    rgba(248, 250, 252, 0.95) 20%,
    rgba(241, 245, 249, 0.85) 40%,
    rgba(226, 232, 240, 0.7) 60%,
    rgba(203, 213, 225, 0.5) 80%,
    rgba(148, 163, 184, 0.3) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0 100px rgba(148, 163, 184, 0.3),
    0 20px 60px rgba(0, 0, 0, 0.1),
    inset 0 0 80px rgba(255, 255, 255, 0.4),
    inset 0 -20px 40px rgba(226, 232, 240, 0.3);
  border: 3px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
}

.slide-image {
  width: 250px;
  height: 200px;
  object-fit: contain;
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.15));
}

.slide-navigation {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.nav-arrow {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  border: 2px solid rgba(226, 232, 240, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
}

.nav-arrow:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(241, 245, 249, 0.95) 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

.nav-arrow svg {
  width: 24px;
  height: 24px;
  fill: #475569;
  transition: all 0.3s ease;
}

.nav-arrow:hover svg {
  fill: #1e293b;
}

.link {
  position: relative;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 12px 24px;
  border-radius: 6px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.link:hover {
  color: #0f172a;
  transform: translateY(-2px);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(241, 245, 249, 0.95) 100%);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.link .underline {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #1e293b 0%, #475569 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.link:hover .underline {
  width: 80%;
}

/* Slide 2 - Home Slider */
.slide-2 {
  background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.fullheight {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 100px 40px 40px;
  position: relative;
}

.texts-top {
  text-align: center;
  color: white;
  margin-bottom: 60px;
}

.texts-top h1 {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 72px;
  line-height: 76px;
  font-weight: 300;
  margin: 0 0 30px 0;
  color: white;
  letter-spacing: -2px;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.texts-top p {
  font-family: 'Inter', sans-serif;
  font-size: 24px;
  line-height: 32px;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 0.5px;
}

.texts-bottom {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 40px;
  flex-wrap: wrap;
}

.slide-text {
  text-align: center;
  max-width: 220px;
  opacity: 0.7;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 20px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.slide-text.active {
  opacity: 1;
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.slide-title {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 18px;
  font-weight: 400;
  margin: 0 0 12px 0;
  color: white;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slide-caption {
  font-family: 'Inter', sans-serif;
  margin: 0;
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  line-height: 20px;
  font-weight: 300;
  letter-spacing: 0.3px;
}

.slideshow {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.elipse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.items-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.items {
  position: relative;
  width: 300px;
  height: 300px;
}

.slide-picture {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  opacity: 0;
  transition: all 0.5s ease;
}

.slide-picture.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.slide-shadow {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 30px;
  opacity: 0.3;
}

.slide-overflow {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slide-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
}

.slide-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  opacity: 0.5;
  pointer-events: none;
}

.controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.prev, .next {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.prev:hover, .next:hover {
  background: rgba(255, 255, 255, 0.3);
}

.prev .icon {
  transform: rotate(180deg);
}

.icon {
  font-family: 'icons';
  font-size: 16px;
  color: white;
}

/* Footer Slide */
.slide-5 {
  background: #51301d;
}

/* Animation Classes */
.letter {
  display: inline-block;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.row {
  display: block;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

/* Smooth transitions for all interactive elements */
.button,
.link,
.slide-picture,
.slide-text {
  transition: all 0.3s ease;
}

/* Enhanced hover effects */
.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.slide-picture:hover {
  transform: translate(-50%, -50%) scale(1.05);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .texts-bottom {
    gap: 20px;
  }

  .slide-text {
    max-width: 150px;
  }

  .slideshow {
    height: 300px;
  }

  .items {
    width: 250px;
    height: 250px;
  }

  .slide-picture {
    width: 150px;
    height: 150px;
  }

  .fs-h1 {
    font-size: 36px;
    line-height: 38px;
  }

  .fs-labeur {
    font-size: 18px;
    line-height: 22px;
  }
}

@media (max-width: 767px) {
  .layout {
    padding: 0 20px;
  }

  .slide-inner {
    padding: 20px;
  }

  .fullheight {
    padding: 80px 20px 20px;
  }

  .texts-bottom {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .slide-text {
    max-width: 300px;
  }

  .slideshow {
    height: 250px;
  }

  .items {
    width: 200px;
    height: 200px;
  }

  .slide-picture {
    width: 120px;
    height: 120px;
  }

  .elipse.hide-desktop {
    display: block;
  }

  .elipse.hide-mobile {
    display: none;
  }

  .slide-indicators {
    right: 20px;
    gap: 10px;
  }

  .slide-indicator {
    width: 10px;
    height: 10px;
  }
}
