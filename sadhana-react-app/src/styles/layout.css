/* Interface and Layout Styles */
#interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  pointer-events: none;
}

#interface img {
  position: absolute;
  pointer-events: none;
}

#interface .top-left {
  top: 40px;
  left: 40px;
}

#interface .top-right {
  top: 40px;
  right: 40px;
}

#interface .bottom-left {
  bottom: 40px;
  left: 40px;
}

#interface .bottom-right {
  bottom: 40px;
  right: 40px;
}

#interface .rotated {
  transform: rotate(180deg);
}

.scroll-to-explore-wrapper {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #51301d;
  pointer-events: auto;
  cursor: pointer;
}

.scroll-to-explore-wrapper span {
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.scroll-to-explore-wrapper .icon {
  width: 12px;
  height: 12px;
}

#button-mute {
  position: fixed;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  cursor: pointer;
  pointer-events: auto;
  z-index: 100;
}

#button-mute i {
  display: block;
  width: 20px;
  height: 2px;
  background: #51301d;
  margin: 4px auto;
  transition: all 0.3s ease;
}

#button-mute.muted i:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

#button-mute.muted i:nth-child(2) {
  opacity: 0;
}

#button-mute.muted i:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Header Styles */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: auto;
}

.header-background-collapsed {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

header.collapsed .header-background-collapsed {
  opacity: 1;
}

header .left {
  display: flex;
  align-items: center;
}

header .logo {
  height: 24px;
  width: auto;
}

header .logo-dark {
  display: block;
}

header .logo-white {
  display: none;
}

header[data-color="light"] .logo-dark {
  display: none;
}

header[data-color="light"] .logo-white {
  display: block;
}

header .right {
  display: flex;
  align-items: center;
  gap: 30px;
}

.route-items {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.route-items li {
  position: relative;
}

.route-items a {
  font-family: Arial, sans-serif;
  font-size: 16px;
  color: #51301d;
  text-decoration: none;
  transition: color 0.3s ease;
}

.route-items a:hover {
  color: #a77b2a;
}

.route-items img {
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.route-items li:hover img {
  opacity: 1;
}

.language-selector {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
  gap: 10px;
}

.language-selector a {
  font-family: Arial, sans-serif;
  font-size: 16px;
  color: #51301d;
  text-decoration: none;
  transition: color 0.3s ease;
}

.language-selector .selected a {
  font-weight: bold;
}

.button-bag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  color: #51301d;
  font-size: 20px;
}

.button {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  font-family: Arial, sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.button.brown {
  background: #51301d;
  color: white;
  border-color: #51301d;
}

.button.brown:hover {
  background: #a77b2a;
  border-color: #a77b2a;
}

.button.transparent {
  background: transparent;
  color: #51301d;
  border-color: #51301d;
}

.button.transparent:hover {
  background: #51301d;
  color: white;
}

.button .picture-hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.button:hover .picture-hover {
  opacity: 1;
}

.button-icon {
  width: 20px;
  height: 20px;
}

/* Mobile Menu */
.button-menu {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-family: Arial, sans-serif;
  font-size: 16px;
  color: #51301d;
}

.button-menu .relative {
  position: relative;
  width: 32px;
  height: 32px;
}

.button-menu .menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 32px;
  height: 32px;
  transition: opacity 0.3s ease;
}

.button-menu .menu-white {
  opacity: 0;
}

.button-menu.active .menu-dark {
  opacity: 0;
}

.button-menu.active .menu-white {
  opacity: 1;
}

/* Responsive Styles */
.hide-mobile {
  display: block;
}

.hide-tablet {
  display: block;
}

.hide-desktop {
  display: none;
}

@media (max-width: 1024px) {
  .hide-tablet {
    display: none;
  }
}

@media (max-width: 767px) {
  .hide-mobile {
    display: none;
  }
  
  .hide-desktop {
    display: block;
  }
  
  header {
    padding: 20px;
  }
  
  #interface .top-left,
  #interface .top-right,
  #interface .bottom-left,
  #interface .bottom-right {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
  }
  
  #button-mute {
    right: 20px;
  }
}
