import React from 'react';
import WelcomeSlide from '../Slides/WelcomeSlide';
import AwakenSlide from '../Slides/AwakenSlide';
import ReturnSlide from '../Slides/ReturnSlide';
import PossibleSlide from '../Slides/PossibleSlide';
import FooterSlide from '../Slides/FooterSlide';
import useSlideNavigation from '../../hooks/useSlideNavigation';

const Homepage = ({ onOpenAppPopup }) => {
  const { currentSlide } = useSlideNavigation(5);

  const slides = [
    { component: WelcomeSlide, background: "image:home1;color:true;center:0.35,0.5;position:-0.1,0" },
    { component: AwakenSlide, background: "image:home2;color:true;center:0.8,0.5" },
    { component: ReturnSlide, background: "image:home3;color:true;center:0.35,0.5" },
    { component: PossibleSlide, background: "image:home4;color:true;center:0.75,0.5" },
    { component: FooterSlide, background: "" }
  ];

  return (
    <div id="root" className="content lang-en_US">
      <section 
        className="page homepage relative" 
        data-component="Homepage" 
        data-interfacescrollposition="right" 
        data-gl-background-global="lens:1;paperA:1" 
        data-postid="273"
      >
        {slides.map((slide, index) => {
          const SlideComponent = slide.component;
          const isActive = index === currentSlide;
          const slideDirection = index < currentSlide ? 'pageslide-left' : 'pageslide-right';

          return (
            <div
              key={index}
              className={`fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${slideDirection}`}
              data-gl-background={slide.background}
              style={{
                zIndex: isActive ? 10 : 1,
                opacity: isActive ? 1 : 0,
                transform: isActive ? 'translateX(0)' :
                          index < currentSlide ? 'translateX(-100px)' : 'translateX(100px)'
              }}
            >
              <SlideComponent
                isActive={isActive}
                onOpenAppPopup={onOpenAppPopup}
              />
            </div>
          );
        })}
      </section>
    </div>
  );
};

export default Homepage;
