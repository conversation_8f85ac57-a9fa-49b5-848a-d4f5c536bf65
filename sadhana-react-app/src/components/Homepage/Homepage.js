import React from 'react';
import WelcomeSlide from '../Slides/WelcomeSlide';
import AwakenSlide from '../Slides/AwakenSlide';
import ReturnSlide from '../Slides/ReturnSlide';
import PossibleSlide from '../Slides/PossibleSlide';
import FooterSlide from '../Slides/FooterSlide';
import useSlideNavigation from '../../hooks/useSlideNavigation';

const Homepage = ({ onOpenAppPopup }) => {
  const { currentSlide, goToSlide } = useSlideNavigation(5);

  const slides = [
    { component: WelcomeSlide, background: "image:home1;color:true;center:0.35,0.5;position:-0.1,0" },
    { component: AwakenSlide, background: "image:home2;color:true;center:0.8,0.5" },
    { component: ReturnSlide, background: "image:home3;color:true;center:0.35,0.5" },
    { component: PossibleSlide, background: "image:home4;color:true;center:0.75,0.5" },
    { component: FooterSlide, background: "" }
  ];

  return (
    <div className="homepage">
      {slides.map((slide, index) => {
        const SlideComponent = slide.component;
        const isActive = index === currentSlide;
        const slideDirection = index < currentSlide ? 'pageslide-up' : 'pageslide-down';

        return (
          <div
            key={index}
            className={`fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${!isActive ? slideDirection : ''}`}
            data-gl-background={slide.background}
            style={{
              zIndex: isActive ? 10 : 1,
              opacity: isActive ? 1 : 0,
              transform: isActive ? 'translateY(0)' :
                        index < currentSlide ? 'translateY(-100vh)' : 'translateY(100vh)',
              backgroundColor: index === 4 ? '#51301d' : 'transparent' // Footer slide background
            }}
          >
            <SlideComponent
              isActive={isActive}
              onOpenAppPopup={onOpenAppPopup}
            />
          </div>
        );
      })}

      {/* Slide Indicators */}
      <div className="slide-indicators">
        {slides.map((_, index) => (
          <div
            key={index}
            className={`slide-indicator ${index === currentSlide ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            title={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default Homepage;
