import React from 'react';
import WelcomeSlide from '../Slides/WelcomeSlide';
import AwakenSlide from '../Slides/AwakenSlide';
import ReturnSlide from '../Slides/ReturnSlide';
import PossibleSlide from '../Slides/PossibleSlide';
import FooterSlide from '../Slides/FooterSlide';
import useSlideNavigation from '../../hooks/useSlideNavigation';

const Homepage = ({ onOpenAppPopup }) => {
  const { currentSlide, goToSlide } = useSlideNavigation(5);

  console.log('Homepage rendering, currentSlide:', currentSlide);

  const slides = [
    {
      component: WelcomeSlide,
      backgroundImage: '/images/slide1-bg.jpg',
      backgroundPosition: 'center 35%'
    },
    {
      component: AwakenSlide,
      backgroundImage: '/images/slide2-bg.jpg',
      backgroundPosition: 'center 80%'
    },
    {
      component: ReturnSlide,
      backgroundImage: '/images/slide3-bg.jpg',
      backgroundPosition: 'center 35%'
    },
    {
      component: PossibleSlide,
      backgroundImage: '/images/slide4-bg.jpg',
      backgroundPosition: 'center 75%'
    },
    {
      component: FooterSlide,
      backgroundImage: '/images/slide5-bg.jpg',
      backgroundPosition: 'center',
      backgroundColor: '#51301d'
    }
  ];

  return (
    <div className="homepage">
      {slides.map((slide, index) => {
        const SlideComponent = slide.component;
        const isActive = index === currentSlide;
        const slideDirection = index < currentSlide ? 'pageslide-up' : 'pageslide-down';

        console.log(`Slide ${index}: isActive=${isActive}, currentSlide=${currentSlide}`);

        return (
          <div
            key={index}
            className={`fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${!isActive ? slideDirection : ''}`}
            style={{
              zIndex: isActive ? 10 : 1,
              opacity: isActive ? 1 : 0,
              transform: isActive ? 'translateY(0)' :
                        index < currentSlide ? 'translateY(-100vh)' : 'translateY(100vh)',
              backgroundImage: slide.backgroundImage ? `url(${slide.backgroundImage})` : 'none',
              backgroundSize: 'cover',
              backgroundPosition: slide.backgroundPosition || 'center',
              backgroundColor: slide.backgroundColor || 'transparent'
            }}
          >
            <SlideComponent
              isActive={isActive}
              onOpenAppPopup={onOpenAppPopup}
            />
          </div>
        );
      })}

      {/* Slide Indicators */}
      <div className="slide-indicators">
        {slides.map((_, index) => (
          <div
            key={index}
            className={`slide-indicator ${index === currentSlide ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            title={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default Homepage;
