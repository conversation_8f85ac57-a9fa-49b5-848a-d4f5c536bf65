import React from 'react';

const Footer = ({ onOpenAppPopup }) => {
  const footerLinks = [
    { href: "/terms", text: "Terms of Service", target: "" },
    { href: "/privacy", text: "Privacy Policy", target: "" },
    { href: "/warranty", text: "Lifetime Warranty", target: "" },
    { href: "/care-guide", text: "Jewelry Care", target: "" },
    { href: "/about", text: "About Us", target: "" }
  ];

  const socialLinks = [
    {
      href: "https://twitter.com/DiamondAtelier",
      icon: "icon-twitter",
      target: "_blank"
    },
    {
      href: "https://www.instagram.com/diamond_atelier_official/",
      icon: "icon-instagram",
      target: "_blank"
    },
    {
      href: "https://www.facebook.com/DiamondAtelier",
      icon: "icon-facebook",
      target: "_blank"
    }
  ];

  return (
    <div id="footer-wrapper" className="page-size-scrolling-unit">
      <footer>
        <div className="content">
          <img
            className="emblem"
            src="/images/diamond-logo-white.svg"
            alt="Diamond Atelier"
            width="180"
            height="120"
          />

          <div className="contact-info">
            <div className="contact-section">
              <h3>Visit Our Showroom</h3>
              <p>123 Diamond District<br />New York, NY 10036<br />United States</p>
            </div>

            <div className="contact-section">
              <h3>Contact Us</h3>
              <p>Phone: +1 (555) 123-GEMS<br />Email: <EMAIL><br />Hours: Mon-Sat 10AM-7PM</p>
            </div>
          </div>

          <div className="services-info">
            <div className="service-item">
              <h4>Custom Design</h4>
              <p>Bespoke jewelry crafted to your vision</p>
            </div>

            <div className="service-item">
              <h4>Expert Consultation</h4>
              <p>Professional guidance for your perfect piece</p>
            </div>

            <div className="service-item">
              <h4>Lifetime Warranty</h4>
              <p>Comprehensive coverage for all our creations</p>
            </div>
          </div>
        </div>

        <ul className="bottom-links">
          <div style={{ maxWidth: '650px' }}>
            {footerLinks.map((link, index) => (
              <li key={index}>
                <a href={link.href} target={link.target}>
                  {link.text}
                </a>
              </li>
            ))}
            
            <li className="social hide-mobile">
              <span>Follow us</span>
              {socialLinks.map((social, index) => (
                <a 
                  key={index}
                  href={social.href} 
                  target={social.target}
                  rel="noopener noreferrer"
                >
                  <i className={`icon ${social.icon}`}></i>
                </a>
              ))}
            </li>
          </div>
        </ul>
      </footer>
    </div>
  );
};

export default Footer;
