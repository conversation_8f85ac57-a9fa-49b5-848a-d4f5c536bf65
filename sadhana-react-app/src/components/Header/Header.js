import React, { useState, useEffect } from 'react';

const Header = ({ onToggleMobileMenu, onOpenAppPopup, isMobileMenuOpen }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [colorTheme] = useState('dark');

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 50;
      setIsScrolled(scrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    { href: "/?page_id=718", text: "About", target: "" },
    { href: "/?page_id=52", text: "Get involved", target: "" },
    { href: "/?page_id=59", text: "Blog", target: "" },
    { href: "/?page_id=54", text: "Contact", target: "" },
    { href: "/?page_id=1720", text: "FAQ", target: "" }
  ];

  return (
    <header 
      data-color={colorTheme} 
      className={isScrolled ? 'collapsed' : ''}
    >
      <div className="header-background-collapsed"></div>
      
      <div className="left">
        <a href="/">
          <img 
            className="logo logo-dark" 
            src="/images/logo-dark.png" 
            alt="Logo sadhana" 
            width="80" 
            height="24" 
          />
          <img 
            className="logo logo-white" 
            src="/images/logo-white.png" 
            alt="Logo sadhana" 
            width="80" 
            height="24" 
          />
        </a>
      </div>

      <div className="right hide-mobile">
        <ul className="route-items">
          {navigationItems.map((item, index) => (
            <li key={index}>
              <a href={item.href} target={item.target}>
                {item.text}
              </a>
              <img src="/images/underline.png" alt="" />
            </li>
          ))}
        </ul>

        <ul className="language-selector">
          <li className="selected">
            <a href="/" className="no-history">A</a>
          </li>
          <li className="separator">/</li>
          <li>
            <a href="/?page_id=713&lang=hi" className="no-history">
              <svg width="18" height="16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.89 4.92c0-1.633-.055-2.64-.164-3.024-2.453 0-4.912-.02-7.35.287l-.04-.082c.04-.328.04-.79 0-1.384l.04-.061c.335.04.756.061 1.262.061L15.896.78c.506 0 .926-.02 1.261-.062l.041.062c-.041.594-.041 1.056 0 1.384l-.041.082c-.964-.191-5.433-.349-7.299-.349-.109.383-.164 1.391-.164 3.025v6.044c0 1.75.072 3.15.216 4.203l-.052.062a7.074 7.074 0 0 0-1.066-.062c-.458 0-.813.02-1.066.062l-.051-.062c.143-.998.215-2.399.215-4.203V4.92Z" fill="#51301D"/>
                <path d="M8.343 9.064c-.542.243-1.438 1.204-2.653 1.204-.958 0-1.986-.245-2.816-.785-.83-.546-1.246-1.285-1.246-2.22 0-.881.42-1.598 1.259-2.15.848-.556 1.666-.759 2.803-.759.918 0 2.076.298 2.653.507v1.232c-.714-.51-1.87-.98-2.653-.98-.597 0-1.365.14-2.003.569-.63.429-.944.891-.944 1.582 0 .719.314 1.154.944 1.584.63.43 1.225.463 2.003.463 1.038 0 1.958-.853 2.653-1.416v1.169ZM12.106 6.65c-1.262 0-2.055.972-2.537 1.442V6.715c.576-.54 1.275-1.122 2.537-1.122 1.261 0 3.356.623 3.356 2.617 0 2.937-1.672 3.29-2.538 3.317V9.948c1.277-.032 1.439-.819 1.439-1.738 0-1.102-1.534-1.56-2.257-1.56Z" fill="#51301D"/>
              </svg>
            </a>
          </li>
        </ul>

        <a href="https://launch.sadhana.app/shop" className="button-bag">
          <i className="icon icon-bag"></i>
        </a>

        <button className="button button-download brown" onClick={onOpenAppPopup}>
          <img src="/images/hover-dark.png" className="picture-hover" alt="" loading="lazy" />
          <span>Download the app</span>
          <img src="/images/emblem_1.png" className="button-icon" alt="" />
        </button>
      </div>

      <div className="right hide-tablet hide-desktop">
        <div 
          className={`button-menu ${isMobileMenuOpen ? 'active' : ''}`}
          onClick={onToggleMobileMenu}
        >
          <span>Menu</span>
          <div className="relative">
            <img 
              className="menu menu-dark" 
              src="/images/menu-dark.png" 
              alt="" 
              width="32" 
              height="32" 
            />
            <img 
              className="menu menu-white" 
              src="/images/cross.png" 
              alt="" 
              width="32" 
              height="32" 
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
