import React, { useState, useEffect } from 'react';

const Header = ({ onToggleMobileMenu, onOpenAppPopup, isMobileMenuOpen }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [colorTheme] = useState('dark');

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 50;
      setIsScrolled(scrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    { href: "/collections", text: "Collections", target: "" },
    { href: "/custom-design", text: "Custom Design", target: "" },
    { href: "/about", text: "About", target: "" },
    { href: "/services", text: "Services", target: "" },
    { href: "/contact", text: "Contact", target: "" }
  ];

  return (
    <header 
      data-color={colorTheme} 
      className={isScrolled ? 'collapsed' : ''}
    >
      <div className="header-background-collapsed"></div>
      
      <div className="left">
        <a href="/">
          <img
            className="logo logo-dark"
            src="/images/diamond-logo-dark.png"
            alt="Diamond Atelier"
            width="120"
            height="32"
          />
          <img
            className="logo logo-white"
            src="/images/diamond-logo-white.png"
            alt="Diamond Atelier"
            width="120"
            height="32"
          />
        </a>
      </div>

      <div className="right hide-mobile">
        <ul className="route-items">
          {navigationItems.map((item, index) => (
            <li key={index}>
              <a href={item.href} target={item.target}>
                {item.text}
              </a>
              <img src="/images/underline.png" alt="" />
            </li>
          ))}
        </ul>

        <a href="/appointment" className="button-appointment">
          <i className="icon icon-calendar"></i>
          <span>Book Consultation</span>
        </a>

        <button className="button button-contact elegant">
          <img src="/images/hover-light.png" className="picture-hover" alt="" loading="lazy" />
          <span>Contact Us</span>
          <img src="/images/diamond-icon.png" className="button-icon" alt="" />
        </button>
      </div>

      <div className="right hide-tablet hide-desktop">
        <div 
          className={`button-menu ${isMobileMenuOpen ? 'active' : ''}`}
          onClick={onToggleMobileMenu}
        >
          <span>Menu</span>
          <div className="relative">
            <img 
              className="menu menu-dark" 
              src="/images/menu-dark.png" 
              alt="" 
              width="32" 
              height="32" 
            />
            <img 
              className="menu menu-white" 
              src="/images/cross.png" 
              alt="" 
              width="32" 
              height="32" 
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
