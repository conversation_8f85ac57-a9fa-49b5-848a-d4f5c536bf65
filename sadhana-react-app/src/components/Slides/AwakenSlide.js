import React, { useState, useEffect } from 'react';

const AwakenSlide = ({ isActive }) => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const slideData = [
    {
      title: "ENGAGEMENT RINGS",
      caption: "Exquisite engagement rings crafted with the finest diamonds and precious metals.",
      image: "/images/engagement-ring.svg",
      alt: "ENGAGEMENT RINGS"
    },
    {
      title: "WEDDING BANDS",
      caption: "Elegant wedding bands designed to symbolize your eternal love and commitment.",
      image: "/images/wedding-band.svg",
      alt: "WEDDING BANDS"
    },
    {
      title: "CUSTOM DESIGN",
      caption: "Bespoke jewelry pieces created to your exact specifications and desires.",
      image: "/images/custom-design.svg",
      alt: "CUSTOM DESIGN"
    },
    {
      title: "LUXURY COLLECTIONS",
      caption: "Curated collections of premium diamonds and luxury jewelry pieces.",
      image: "/images/luxury-collection.svg",
      alt: "LUXURY COLLECTIONS"
    },
    {
      title: "DIAMOND CERTIFICATION",
      caption: "Expert diamond grading and certification services with detailed authenticity reports.",
      image: "/images/diamond-cert.svg",
      alt: "DIAMOND CERTIFICATION"
    }
  ];

  useEffect(() => {
    if (!isActive || !isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlideIndex(prev => (prev + 1) % slideData.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isActive, isAutoPlaying, slideData.length]);

  const goToPrevious = () => {
    setIsAutoPlaying(false);
    setCurrentSlideIndex(prev => 
      prev === 0 ? slideData.length - 1 : prev - 1
    );
    setTimeout(() => setIsAutoPlaying(true), 5000);
  };

  const goToNext = () => {
    setIsAutoPlaying(false);
    setCurrentSlideIndex(prev => (prev + 1) % slideData.length);
    setTimeout(() => setIsAutoPlaying(true), 5000);
  };

  return (
    <div className="slide-inner fullheight" data-component="HomeSlider">
      <div className="texts-top">
        <h1 className="fs-h1" data-component="splitLetter">
          Discover Excellence
        </h1>
        <p className="fs-labeur-little" data-component="splitRow">
          Experience the finest diamonds and luxury jewelry craftsmanship.
        </p>
      </div>

      <div className="texts-bottom">
        {slideData.map((slide, index) => (
          <div 
            key={index}
            className={`slide-text ${index === currentSlideIndex ? 'active' : ''}`}
          >
            <h3 className="slide-title fs-h3">{slide.title}</h3>
            <p className="slide-caption fs-labeur-little">{slide.caption}</p>
          </div>
        ))}
      </div>

      <div className="slideshow">
        <svg viewBox="0 0 600 1080" className="elipse hide-mobile hide-tablet">
          <defs>
            <linearGradient id="fadeGrad" y2="1" x2="0">
              <stop offset="0" stopColor="white" stopOpacity="0.0"></stop>
              <stop offset="0.3" stopColor="white" stopOpacity="0.4"></stop>
              <stop offset="0.5" stopColor="white" stopOpacity="0.4"></stop>
              <stop offset="1" stopColor="white" stopOpacity="0"></stop>
            </linearGradient>
            <mask id="fade" maskContentUnits="userSpaceOnUse">
              <rect width="600" height="100%" fill="url(#fadeGrad)"></rect>
            </mask>
          </defs>
          <circle 
            mask="url(#fade)" 
            cx="-600" 
            cy="540" 
            r="916" 
            fill="none" 
            stroke="#484848" 
            strokeWidth="3"
          ></circle>
        </svg>

        <svg viewBox="0 0 360 235" className="elipse hide-desktop">
          <defs>
            <linearGradient id="fadeGrad2" y2="1" x2="0">
              <stop offset="0" stopColor="white" stopOpacity="0.0"></stop>
              <stop offset="0.3" stopColor="white" stopOpacity="0.4"></stop>
              <stop offset="0.3" stopColor="white" stopOpacity="0.4"></stop>
              <stop offset="1" stopColor="white" stopOpacity="0"></stop>
            </linearGradient>
            <mask id="fade2" maskContentUnits="userSpaceOnUse">
              <rect width="600" height="100%" fill="url(#fadeGrad2)"></rect>
            </mask>
          </defs>
          <circle 
            mask="url(#fade2)" 
            cx="-312" 
            cy="875" 
            r="916" 
            fill="none" 
            stroke="#484848" 
            strokeWidth="3"
          ></circle>
        </svg>

        <div className="items-wrapper">
          <div className="items">
            {slideData.map((slide, index) => (
              <div 
                key={index}
                className={`slide-picture ${index === currentSlideIndex ? 'active' : ''}`}
              >
                <img className="slide-shadow" src="/images/shadow.png" alt="" />
                <div className="slide-overflow">
                  <img className="slide-background" src="/images/background_1.png" alt="" />
                  <img className="slide-img" src={slide.image} alt={slide.alt} />
                </div>
                <img className="slide-glow" src="/images/glow.png" alt="" />
              </div>
            ))}
          </div>

          <div className="controls">
            <div className="prev" onClick={goToPrevious}>
              <i className="icon icon-triangle"></i>
              <span>
                <i className="icon icon-triangle"></i>
              </span>
            </div>
            <div className="next" onClick={goToNext}>
              <i className="icon icon-triangle"></i>
              <span>
                <i className="icon icon-triangle"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AwakenSlide;
