/* App Component Styles */
.App {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Interactive Background */
.interactive-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.interactive-background canvas {
  width: 100%;
  height: 100%;
}

/* Global Loader */
#sadhana-global-loader {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease;
}

#sadhana-global-loader.hidden {
  opacity: 0;
  pointer-events: none;
}

#sadhana-global-loader canvas {
  background-color: transparent;
}
