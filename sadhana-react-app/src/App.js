import React, { useState, useEffect } from 'react';
import Header from './components/Header/Header';
import Navigation from './components/Navigation/Navigation';
import Homepage from './components/Homepage/Homepage';
import Footer from './components/Footer/Footer';
import Interface from './components/Interactive/Interface';
import AppDownloadPopup from './components/Interactive/AppDownloadPopup';
import CookieBanner from './components/Interactive/CookieBanner';
import VideoPlayer from './components/Interactive/VideoPlayer';
import './App.css';
import './styles/layout.css';
import './styles/homepage.css';
import './styles/interactive.css';

function App() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAppPopupOpen, setIsAppPopupOpen] = useState(false);
  const [isCookieBannerVisible, setIsCookieBannerVisible] = useState(true);
  const [isVideoPlayerOpen, setIsVideoPlayerOpen] = useState(false);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      document.body.classList.add('loaded');
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const openAppPopup = () => {
    setIsAppPopupOpen(true);
  };

  const closeAppPopup = () => {
    setIsAppPopupOpen(false);
  };

  const closeCookieBanner = () => {
    setIsCookieBannerVisible(false);
  };

  const closeVideoPlayer = () => {
    setIsVideoPlayerOpen(false);
  };

  return (
    <div className="App">
      <Interface />

      <Header
        onToggleMobileMenu={toggleMobileMenu}
        onOpenAppPopup={openAppPopup}
        isMobileMenuOpen={isMobileMenuOpen}
      />

      <Navigation
        isOpen={isMobileMenuOpen}
        onClose={toggleMobileMenu}
        onOpenAppPopup={openAppPopup}
      />

      <Homepage
        onOpenAppPopup={openAppPopup}
      />

      <Footer onOpenAppPopup={openAppPopup} />

      <AppDownloadPopup
        isOpen={isAppPopupOpen}
        onClose={closeAppPopup}
      />

      {isCookieBannerVisible && (
        <CookieBanner onClose={closeCookieBanner} />
      )}

      <VideoPlayer
        isOpen={isVideoPlayerOpen}
        onClose={closeVideoPlayer}
      />

      <audio id="background-audio" loop preload="none">
        <source src="/media/araj.mp3" type="audio/mpeg" />
      </audio>
    </div>
  );
}

export default App;
