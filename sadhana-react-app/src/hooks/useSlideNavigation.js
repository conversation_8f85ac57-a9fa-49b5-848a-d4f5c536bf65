import { useState, useEffect, useCallback } from 'react';

const useSlideNavigation = (totalSlides = 5) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  const goToSlide = useCallback((slideIndex) => {
    if (slideIndex >= 0 && slideIndex < totalSlides && !isScrolling) {
      setIsScrolling(true);
      setCurrentSlide(slideIndex);
      
      setTimeout(() => {
        setIsScrolling(false);
      }, 800);
    }
  }, [totalSlides, isScrolling]);

  const goToNextSlide = useCallback(() => {
    if (currentSlide < totalSlides - 1) {
      goToSlide(currentSlide + 1);
    }
  }, [currentSlide, totalSlides, goToSlide]);

  const goToPreviousSlide = useCallback(() => {
    if (currentSlide > 0) {
      goToSlide(currentSlide - 1);
    }
  }, [currentSlide, goToSlide]);

  useEffect(() => {
    const handleWheel = (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (isScrolling) return;

      // Add a small threshold to prevent accidental scrolling
      if (Math.abs(e.deltaY) < 10) return;

      if (e.deltaY > 0) {
        goToNextSlide();
      } else if (e.deltaY < 0) {
        goToPreviousSlide();
      }
    };

    const handleKeyDown = (e) => {
      if (isScrolling) return;

      switch (e.key) {
        case 'ArrowDown':
        case 'PageDown':
        case ' ': // Space key
          e.preventDefault();
          goToNextSlide();
          break;
        case 'ArrowUp':
        case 'PageUp':
          e.preventDefault();
          goToPreviousSlide();
          break;
        case 'Home':
          e.preventDefault();
          goToSlide(0);
          break;
        case 'End':
          e.preventDefault();
          goToSlide(totalSlides - 1);
          break;
        default:
          break;
      }
    };

    const handleTouchStart = (e) => {
      const touch = e.touches[0];
      window.touchStartY = touch.clientY;
    };

    const handleTouchEnd = (e) => {
      if (!window.touchStartY || isScrolling) return;

      const touch = e.changedTouches[0];
      const touchEndY = touch.clientY;
      const deltaY = window.touchStartY - touchEndY;

      // Minimum swipe distance
      if (Math.abs(deltaY) < 50) return;

      if (deltaY > 0) {
        goToNextSlide();
      } else {
        goToPreviousSlide();
      }

      window.touchStartY = null;
    };

    // Prevent default scroll behavior on the body
    document.body.style.overflow = 'hidden';

    // Add event listeners
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('touchstart', handleTouchStart, { passive: true });
    window.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.body.style.overflow = 'unset';
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isScrolling, goToNextSlide, goToPreviousSlide, goToSlide, totalSlides]);

  return {
    currentSlide,
    isScrolling,
    goToSlide,
    goToNextSlide,
    goToPreviousSlide
  };
};

export default useSlideNavigation;
