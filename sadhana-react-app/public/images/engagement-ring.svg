<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="diamondGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8f9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e8f0ff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="goldGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffed4e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffc107;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Ring Band -->
  <ellipse cx="100" cy="150" rx="70" ry="18" fill="url(#goldGrad)" stroke="#e6ac00" stroke-width="2"/>
  <rect x="30" y="132" width="140" height="36" fill="url(#goldGrad)" stroke="#e6ac00" stroke-width="2"/>
  <ellipse cx="100" cy="132" rx="70" ry="18" fill="url(#goldGrad)" stroke="#e6ac00" stroke-width="2"/>
  
  <!-- Main Diamond -->
  <polygon points="100,40 125,75 115,110 85,110 75,75" fill="url(#diamondGrad)" stroke="#b3d9ff" stroke-width="2"/>
  
  <!-- Side Diamonds -->
  <polygon points="70,85 80,95 75,105 65,105 60,95" fill="url(#diamondGrad)" stroke="#b3d9ff" stroke-width="1"/>
  <polygon points="130,85 140,95 135,105 125,105 120,95" fill="url(#diamondGrad)" stroke="#b3d9ff" stroke-width="1"/>
  
  <!-- Diamond Sparkles -->
  <circle cx="95" cy="65" r="2" fill="#ffffff" opacity="0.9"/>
  <circle cx="108" cy="70" r="1.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="100" cy="85" r="1" fill="#ffffff" opacity="0.7"/>
  <circle cx="70" cy="95" r="1" fill="#ffffff" opacity="0.6"/>
  <circle cx="130" cy="95" r="1" fill="#ffffff" opacity="0.6"/>
  
  <!-- Ring Setting -->
  <ellipse cx="100" cy="120" rx="35" ry="10" fill="url(#goldGrad)" stroke="#e6ac00" stroke-width="1"/>
</svg>
