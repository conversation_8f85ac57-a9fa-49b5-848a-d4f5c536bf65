<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#f0f8ff;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#e6f3ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dceeff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="ringGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffed4e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffc107;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Ring Band -->
  <ellipse cx="100" cy="140" rx="60" ry="15" fill="url(#ringGradient)" stroke="#e6ac00" stroke-width="2"/>
  <rect x="40" y="125" width="120" height="30" fill="url(#ringGradient)" stroke="#e6ac00" stroke-width="2"/>
  <ellipse cx="100" cy="125" rx="60" ry="15" fill="url(#ringGradient)" stroke="#e6ac00" stroke-width="2"/>
  
  <!-- Diamond -->
  <polygon points="100,60 120,90 110,120 90,120 80,90" fill="url(#diamondGradient)" stroke="#b3d9ff" stroke-width="2"/>
  
  <!-- Diamond Sparkles -->
  <circle cx="95" cy="80" r="2" fill="#ffffff" opacity="0.8"/>
  <circle cx="105" cy="85" r="1.5" fill="#ffffff" opacity="0.9"/>
  <circle cx="100" cy="95" r="1" fill="#ffffff" opacity="0.7"/>
  
  <!-- Ring Setting -->
  <ellipse cx="100" cy="115" rx="25" ry="8" fill="url(#ringGradient)" stroke="#e6ac00" stroke-width="1"/>
</svg>
