{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Navigation/Navigation.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = ({\n  isOpen,\n  onClose,\n  onOpenAppPopup\n}) => {\n  const navigationItems = [{\n    href: \"/?page_id=718\",\n    text: \"About\",\n    target: \"\"\n  }, {\n    href: \"/?page_id=52\",\n    text: \"Get involved\",\n    target: \"\"\n  }, {\n    href: \"/?page_id=59\",\n    text: \"Blog\",\n    target: \"\"\n  }, {\n    href: \"/?page_id=54\",\n    text: \"Contact\",\n    target: \"\"\n  }, {\n    href: \"/?page_id=1720\",\n    text: \"FAQ\",\n    target: \"\"\n  }];\n  const handleLinkClick = () => {\n    onClose();\n  };\n  const handleDownloadClick = () => {\n    onOpenAppPopup();\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    id: \"menu-mobile\",\n    className: isOpen ? 'active' : '',\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content fs-h1\",\n      children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"links\",\n        children: navigationItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            target: item.target,\n            onClick: handleLinkClick,\n            children: item.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"language-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"selected\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/\",\n            className: \"no-history\",\n            onClick: handleLinkClick,\n            children: \"A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"separator\",\n          children: \"/\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/?page_id=713&lang=hi\",\n            className: \"no-history\",\n            onClick: handleLinkClick,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"18\",\n              height: \"16\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M7.89 4.92c0-1.633-.055-2.64-.164-3.024-2.453 0-4.912-.02-7.35.287l-.04-.082c.04-.328.04-.79 0-1.384l.04-.061c.335.04.756.061 1.262.061L15.896.78c.506 0 .926-.02 1.261-.062l.041.062c-.041.594-.041 1.056 0 1.384l-.041.082c-.964-.191-5.433-.349-7.299-.349-.109.383-.164 1.391-.164 3.025v6.044c0 1.75.072 3.15.216 4.203l-.052.062a7.074 7.074 0 0 0-1.066-.062c-.458 0-.813.02-1.066.062l-.051-.062c.143-.998.215-2.399.215-4.203V4.92Z\",\n                fill: \"currentColor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M8.343 9.064c-.542.243-1.438 1.204-2.653 1.204-.958 0-1.986-.245-2.816-.785-.83-.546-1.246-1.285-1.246-2.22 0-.881.42-1.598 1.259-2.15.848-.556 1.666-.759 2.803-.759.918 0 2.076.298 2.653.507v1.232c-.714-.51-1.87-.98-2.653-.98-.597 0-1.365.14-2.003.569-.63.429-.944.891-.944 1.582 0 .719.314 1.154.944 1.584.63.43 1.225.463 2.003.463 1.038 0 1.958-.853 2.653-1.416v1.169ZM12.106 6.65c-1.262 0-2.055.972-2.537 1.442V6.715c.576-.54 1.275-1.122 2.537-1.122 1.261 0 3.356.623 3.356 2.617 0 2.937-1.672 3.29-2.538 3.317V9.948c1.277-.032 1.439-.819 1.439-1.738 0-1.102-1.534-1.56-2.257-1.56Z\",\n                fill: \"currentColor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button button-download no-icon brown\",\n        onClick: handleDownloadClick,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/hover-dark.png\",\n          className: \"picture-hover\",\n          alt: \"\",\n          loading: \"lazy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Download the app for free\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Navigation", "isOpen", "onClose", "onOpenAppPopup", "navigationItems", "href", "text", "target", "handleLinkClick", "handleDownloadClick", "id", "className", "children", "map", "item", "index", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "fill", "xmlns", "d", "src", "alt", "loading", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Navigation/Navigation.js"], "sourcesContent": ["import React from 'react';\n\nconst Navigation = ({ isOpen, onClose, onOpenAppPopup }) => {\n  const navigationItems = [\n    { href: \"/?page_id=718\", text: \"About\", target: \"\" },\n    { href: \"/?page_id=52\", text: \"Get involved\", target: \"\" },\n    { href: \"/?page_id=59\", text: \"Blog\", target: \"\" },\n    { href: \"/?page_id=54\", text: \"Contact\", target: \"\" },\n    { href: \"/?page_id=1720\", text: \"FAQ\", target: \"\" }\n  ];\n\n  const handleLinkClick = () => {\n    onClose();\n  };\n\n  const handleDownloadClick = () => {\n    onOpenAppPopup();\n    onClose();\n  };\n\n  return (\n    <nav id=\"menu-mobile\" className={isOpen ? 'active' : ''}>\n      <div className=\"content fs-h1\">\n        <ul className=\"links\">\n          {navigationItems.map((item, index) => (\n            <li key={index}>\n              <a \n                href={item.href} \n                target={item.target}\n                onClick={handleLinkClick}\n              >\n                {item.text}\n              </a>\n            </li>\n          ))}\n        </ul>\n\n        <ul className=\"language-selector\">\n          <li className=\"selected\">\n            <a href=\"/\" className=\"no-history\" onClick={handleLinkClick}>\n              A\n            </a>\n          </li>\n          <li className=\"separator\">/</li>\n          <li>\n            <a \n              href=\"/?page_id=713&lang=hi\" \n              className=\"no-history\"\n              onClick={handleLinkClick}\n            >\n              <svg width=\"18\" height=\"16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M7.89 4.92c0-1.633-.055-2.64-.164-3.024-2.453 0-4.912-.02-7.35.287l-.04-.082c.04-.328.04-.79 0-1.384l.04-.061c.335.04.756.061 1.262.061L15.896.78c.506 0 .926-.02 1.261-.062l.041.062c-.041.594-.041 1.056 0 1.384l-.041.082c-.964-.191-5.433-.349-7.299-.349-.109.383-.164 1.391-.164 3.025v6.044c0 1.75.072 3.15.216 4.203l-.052.062a7.074 7.074 0 0 0-1.066-.062c-.458 0-.813.02-1.066.062l-.051-.062c.143-.998.215-2.399.215-4.203V4.92Z\" fill=\"currentColor\"/>\n                <path d=\"M8.343 9.064c-.542.243-1.438 1.204-2.653 1.204-.958 0-1.986-.245-2.816-.785-.83-.546-1.246-1.285-1.246-2.22 0-.881.42-1.598 1.259-2.15.848-.556 1.666-.759 2.803-.759.918 0 2.076.298 2.653.507v1.232c-.714-.51-1.87-.98-2.653-.98-.597 0-1.365.14-2.003.569-.63.429-.944.891-.944 1.582 0 .719.314 1.154.944 1.584.63.43 1.225.463 2.003.463 1.038 0 1.958-.853 2.653-1.416v1.169ZM12.106 6.65c-1.262 0-2.055.972-2.537 1.442V6.715c.576-.54 1.275-1.122 2.537-1.122 1.261 0 3.356.623 3.356 2.617 0 2.937-1.672 3.29-2.538 3.317V9.948c1.277-.032 1.439-.819 1.439-1.738 0-1.102-1.534-1.56-2.257-1.56Z\" fill=\"currentColor\"/>\n              </svg>\n            </a>\n          </li>\n        </ul>\n\n        <button \n          className=\"button button-download no-icon brown\"\n          onClick={handleDownloadClick}\n        >\n          <img src=\"/images/hover-dark.png\" className=\"picture-hover\" alt=\"\" loading=\"lazy\" />\n          <span>Download the app for free</span>\n        </button>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAe,CAAC,KAAK;EAC1D,MAAMC,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE;EAAG,CAAC,EACpD;IAAEF,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC1D;IAAEF,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAG,CAAC,EAClD;IAAEF,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,SAAS;IAAEC,MAAM,EAAE;EAAG,CAAC,EACrD;IAAEF,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAG,CAAC,CACpD;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BN,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMO,mBAAmB,GAAGA,CAAA,KAAM;IAChCN,cAAc,CAAC,CAAC;IAChBD,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACEH,OAAA;IAAKW,EAAE,EAAC,aAAa;IAACC,SAAS,EAAEV,MAAM,GAAG,QAAQ,GAAG,EAAG;IAAAW,QAAA,eACtDb,OAAA;MAAKY,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5Bb,OAAA;QAAIY,SAAS,EAAC,OAAO;QAAAC,QAAA,EAClBR,eAAe,CAACS,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/BhB,OAAA;UAAAa,QAAA,eACEb,OAAA;YACEM,IAAI,EAAES,IAAI,CAACT,IAAK;YAChBE,MAAM,EAAEO,IAAI,CAACP,MAAO;YACpBS,OAAO,EAAER,eAAgB;YAAAI,QAAA,EAExBE,IAAI,CAACR;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC,GAPGL,KAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAELrB,OAAA;QAAIY,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC/Bb,OAAA;UAAIY,SAAS,EAAC,UAAU;UAAAC,QAAA,eACtBb,OAAA;YAAGM,IAAI,EAAC,GAAG;YAACM,SAAS,EAAC,YAAY;YAACK,OAAO,EAAER,eAAgB;YAAAI,QAAA,EAAC;UAE7D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACLrB,OAAA;UAAIY,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCrB,OAAA;UAAAa,QAAA,eACEb,OAAA;YACEM,IAAI,EAAC,uBAAuB;YAC5BM,SAAS,EAAC,YAAY;YACtBK,OAAO,EAAER,eAAgB;YAAAI,QAAA,eAEzBb,OAAA;cAAKsB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC,4BAA4B;cAAAZ,QAAA,gBACxEb,OAAA;gBAAM0B,CAAC,EAAC,8aAA8a;gBAACF,IAAI,EAAC;cAAc;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC5crB,OAAA;gBAAM0B,CAAC,EAAC,2kBAA2kB;gBAACF,IAAI,EAAC;cAAc;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtmB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAELrB,OAAA;QACEY,SAAS,EAAC,sCAAsC;QAChDK,OAAO,EAAEP,mBAAoB;QAAAG,QAAA,gBAE7Bb,OAAA;UAAK2B,GAAG,EAAC,wBAAwB;UAACf,SAAS,EAAC,eAAe;UAACgB,GAAG,EAAC,EAAE;UAACC,OAAO,EAAC;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFrB,OAAA;UAAAa,QAAA,EAAM;QAAyB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACS,EAAA,GAlEI7B,UAAU;AAoEhB,eAAeA,UAAU;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}