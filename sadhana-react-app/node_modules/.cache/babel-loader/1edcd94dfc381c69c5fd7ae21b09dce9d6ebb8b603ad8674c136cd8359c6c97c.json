{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/VideoPlayer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlayer = ({\n  isOpen,\n  onClose,\n  videoUrl = ''\n}) => {\n  _s();\n  const iframeRef = useRef(null);\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n\n      // Load video URL if provided\n      if (videoUrl && iframeRef.current) {\n        iframeRef.current.src = videoUrl;\n      }\n    } else {\n      document.body.style.overflow = 'unset';\n\n      // Clear video URL when closing\n      if (iframeRef.current) {\n        iframeRef.current.src = '';\n      }\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose, videoUrl]);\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"videoplayer\",\n    className: isOpen ? 'active' : '',\n    onClick: handleBackdropClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content\",\n      children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n        ref: iframeRef,\n        border: \"0\",\n        width: \"100%\",\n        height: \"100%\",\n        title: \"Video Player\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"close\",\n      onClick: onClose,\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"icon icon-cross\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"xrPm9S+7ob9FwtLVsZjNguOLSgs=\");\n_c = VideoPlayer;\nexport default VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "VideoPlayer", "isOpen", "onClose", "videoUrl", "_s", "iframeRef", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "current", "src", "removeEventListener", "handleBackdropClick", "target", "currentTarget", "id", "className", "onClick", "children", "ref", "border", "width", "height", "title", "allow", "allowFullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/VideoPlayer.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst VideoPlayer = ({ isOpen, onClose, videoUrl = '' }) => {\n  const iframeRef = useRef(null);\n\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n      \n      // Load video URL if provided\n      if (videoUrl && iframeRef.current) {\n        iframeRef.current.src = videoUrl;\n      }\n    } else {\n      document.body.style.overflow = 'unset';\n      \n      // Clear video URL when closing\n      if (iframeRef.current) {\n        iframeRef.current.src = '';\n      }\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose, videoUrl]);\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div \n      id=\"videoplayer\" \n      className={isOpen ? 'active' : ''}\n      onClick={handleBackdropClick}\n    >\n      <div className=\"content\">\n        <iframe \n          ref={iframeRef}\n          border=\"0\" \n          width=\"100%\" \n          height=\"100%\"\n          title=\"Video Player\"\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n          allowFullScreen\n        ></iframe>\n      </div>\n      \n      <div className=\"close\" onClick={onClose}>\n        <i className=\"icon icon-cross\"></i>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoPlayer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAMC,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMU,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBN,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;MAEvC;MACA,IAAIV,QAAQ,IAAIE,SAAS,CAACS,OAAO,EAAE;QACjCT,SAAS,CAACS,OAAO,CAACC,GAAG,GAAGZ,QAAQ;MAClC;IACF,CAAC,MAAM;MACLM,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;;MAEtC;MACA,IAAIR,SAAS,CAACS,OAAO,EAAE;QACrBT,SAAS,CAACS,OAAO,CAACC,GAAG,GAAG,EAAE;MAC5B;IACF;IAEA,OAAO,MAAM;MACXN,QAAQ,CAACO,mBAAmB,CAAC,SAAS,EAAEV,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACZ,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC,CAAC;EAE/B,MAAMc,mBAAmB,GAAIV,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACW,MAAM,KAAKX,CAAC,CAACY,aAAa,EAAE;MAChCjB,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IACEqB,EAAE,EAAC,aAAa;IAChBC,SAAS,EAAEpB,MAAM,GAAG,QAAQ,GAAG,EAAG;IAClCqB,OAAO,EAAEL,mBAAoB;IAAAM,QAAA,gBAE7BxB,OAAA;MAAKsB,SAAS,EAAC,SAAS;MAAAE,QAAA,eACtBxB,OAAA;QACEyB,GAAG,EAAEnB,SAAU;QACfoB,MAAM,EAAC,GAAG;QACVC,KAAK,EAAC,MAAM;QACZC,MAAM,EAAC,MAAM;QACbC,KAAK,EAAC,cAAc;QACpBC,KAAK,EAAC,0FAA0F;QAChGC,eAAe;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAENnC,OAAA;MAAKsB,SAAS,EAAC,OAAO;MAACC,OAAO,EAAEpB,OAAQ;MAAAqB,QAAA,eACtCxB,OAAA;QAAGsB,SAAS,EAAC;MAAiB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA9DIJ,WAAW;AAAAmC,EAAA,GAAXnC,WAAW;AAgEjB,eAAeA,WAAW;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}