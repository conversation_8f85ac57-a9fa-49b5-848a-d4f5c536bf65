{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/Header/Header';\nimport Navigation from './components/Navigation/Navigation';\nimport Homepage from './components/Homepage/Homepage';\nimport Footer from './components/Footer/Footer';\nimport Interface from './components/Interactive/Interface';\nimport AppDownloadPopup from './components/Interactive/AppDownloadPopup';\nimport CookieBanner from './components/Interactive/CookieBanner';\nimport VideoPlayer from './components/Interactive/VideoPlayer';\nimport './App.css';\nimport './styles/layout.css';\nimport './styles/homepage.css';\nimport './styles/interactive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isAppPopupOpen, setIsAppPopupOpen] = useState(false);\n  const [isCookieBannerVisible, setIsCookieBannerVisible] = useState(true);\n  const [isVideoPlayerOpen, setIsVideoPlayerOpen] = useState(false);\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => {\n      document.body.classList.add('loaded');\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n  const openAppPopup = () => {\n    setIsAppPopupOpen(true);\n  };\n  const closeAppPopup = () => {\n    setIsAppPopupOpen(false);\n  };\n  const closeCookieBanner = () => {\n    setIsCookieBannerVisible(false);\n  };\n  const openVideoPlayer = videoUrl => {\n    setIsVideoPlayerOpen(true);\n  };\n  const closeVideoPlayer = () => {\n    setIsVideoPlayerOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(Interface, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {\n      onToggleMobileMenu: toggleMobileMenu,\n      onOpenAppPopup: openAppPopup,\n      isMobileMenuOpen: isMobileMenuOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n      isOpen: isMobileMenuOpen,\n      onClose: toggleMobileMenu,\n      onOpenAppPopup: openAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Homepage, {\n      onOpenAppPopup: openAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      onOpenAppPopup: openAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppDownloadPopup, {\n      isOpen: isAppPopupOpen,\n      onClose: closeAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), isCookieBannerVisible && /*#__PURE__*/_jsxDEV(CookieBanner, {\n      onClose: closeCookieBanner\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(VideoPlayer, {\n      isOpen: isVideoPlayerOpen,\n      onClose: closeVideoPlayer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"audio\", {\n      id: \"background-audio\",\n      loop: true,\n      preload: \"none\",\n      children: /*#__PURE__*/_jsxDEV(\"source\", {\n        src: \"/media/araj.mp3\",\n        type: \"audio/mpeg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"CY7Xpfhb0SoFPSAG9FlghCncilY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "Navigation", "Homepage", "Footer", "Interface", "AppDownloadPopup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VideoPlayer", "jsxDEV", "_jsxDEV", "App", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "isAppPopupOpen", "setIsAppPopupOpen", "isCookieBannerVisible", "setIsCookieBannerVisible", "isVideoPlayerOpen", "setIsVideoPlayerOpen", "timer", "setTimeout", "document", "body", "classList", "add", "clearTimeout", "toggleMobileMenu", "openAppPopup", "closeAppPopup", "closeCookieBanner", "openVideoPlayer", "videoUrl", "closeVideoPlayer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onToggleMobileMenu", "onOpenAppPopup", "isOpen", "onClose", "id", "loop", "preload", "src", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Header from './components/Header/Header';\nimport Navigation from './components/Navigation/Navigation';\nimport Homepage from './components/Homepage/Homepage';\nimport Footer from './components/Footer/Footer';\nimport Interface from './components/Interactive/Interface';\nimport AppDownloadPopup from './components/Interactive/AppDownloadPopup';\nimport CookieBanner from './components/Interactive/CookieBanner';\nimport VideoPlayer from './components/Interactive/VideoPlayer';\nimport './App.css';\nimport './styles/layout.css';\nimport './styles/homepage.css';\nimport './styles/interactive.css';\n\nfunction App() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isAppPopupOpen, setIsAppPopupOpen] = useState(false);\n  const [isCookieBannerVisible, setIsCookieBannerVisible] = useState(true);\n  const [isVideoPlayerOpen, setIsVideoPlayerOpen] = useState(false);\n\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => {\n      document.body.classList.add('loaded');\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  const openAppPopup = () => {\n    setIsAppPopupOpen(true);\n  };\n\n  const closeAppPopup = () => {\n    setIsAppPopupOpen(false);\n  };\n\n  const closeCookieBanner = () => {\n    setIsCookieBannerVisible(false);\n  };\n\n  const openVideoPlayer = (videoUrl) => {\n    setIsVideoPlayerOpen(true);\n  };\n\n  const closeVideoPlayer = () => {\n    setIsVideoPlayerOpen(false);\n  };\n\n  return (\n    <div className=\"App\">\n      <Interface />\n\n      <Header\n        onToggleMobileMenu={toggleMobileMenu}\n        onOpenAppPopup={openAppPopup}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n\n      <Navigation\n        isOpen={isMobileMenuOpen}\n        onClose={toggleMobileMenu}\n        onOpenAppPopup={openAppPopup}\n      />\n\n      <Homepage\n        onOpenAppPopup={openAppPopup}\n      />\n\n      <Footer onOpenAppPopup={openAppPopup} />\n\n      <AppDownloadPopup\n        isOpen={isAppPopupOpen}\n        onClose={closeAppPopup}\n      />\n\n      {isCookieBannerVisible && (\n        <CookieBanner onClose={closeCookieBanner} />\n      )}\n\n      <VideoPlayer\n        isOpen={isVideoPlayerOpen}\n        onClose={closeVideoPlayer}\n      />\n\n      <audio id=\"background-audio\" loop preload=\"none\">\n        <source src=\"/media/araj.mp3\" type=\"audio/mpeg\" />\n      </audio>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,OAAO,WAAW;AAClB,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAC9B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACd;IACA,MAAMqB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,YAAY,CAACN,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzBb,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1Bd,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bb,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;EAED,MAAMc,eAAe,GAAIC,QAAQ,IAAK;IACpCb,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,oBACEV,OAAA;IAAKyB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB1B,OAAA,CAACL,SAAS;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEb9B,OAAA,CAACT,MAAM;MACLwC,kBAAkB,EAAEb,gBAAiB;MACrCc,cAAc,EAAEb,YAAa;MAC7BhB,gBAAgB,EAAEA;IAAiB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAEF9B,OAAA,CAACR,UAAU;MACTyC,MAAM,EAAE9B,gBAAiB;MACzB+B,OAAO,EAAEhB,gBAAiB;MAC1Bc,cAAc,EAAEb;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAEF9B,OAAA,CAACP,QAAQ;MACPuC,cAAc,EAAEb;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAEF9B,OAAA,CAACN,MAAM;MAACsC,cAAc,EAAEb;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExC9B,OAAA,CAACJ,gBAAgB;MACfqC,MAAM,EAAE5B,cAAe;MACvB6B,OAAO,EAAEd;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,EAEDvB,qBAAqB,iBACpBP,OAAA,CAACH,YAAY;MAACqC,OAAO,EAAEb;IAAkB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5C,eAED9B,OAAA,CAACF,WAAW;MACVmC,MAAM,EAAExB,iBAAkB;MAC1ByB,OAAO,EAAEV;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAEF9B,OAAA;MAAOmC,EAAE,EAAC,kBAAkB;MAACC,IAAI;MAACC,OAAO,EAAC,MAAM;MAAAX,QAAA,eAC9C1B,OAAA;QAAQsC,GAAG,EAAC,iBAAiB;QAACC,IAAI,EAAC;MAAY;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAC5B,EAAA,CAhFQD,GAAG;AAAAuC,EAAA,GAAHvC,GAAG;AAkFZ,eAAeA,GAAG;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}