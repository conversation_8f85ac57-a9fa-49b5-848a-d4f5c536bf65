{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Header/Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onToggleMobileMenu,\n  onOpenAppPopup,\n  isMobileMenuOpen\n}) => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [colorTheme] = useState('dark');\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrolled = window.scrollY > 50;\n      setIsScrolled(scrolled);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigationItems = [{\n    href: \"/collections\",\n    text: \"Collections\",\n    target: \"\"\n  }, {\n    href: \"/custom-design\",\n    text: \"Custom Design\",\n    target: \"\"\n  }, {\n    href: \"/about\",\n    text: \"About\",\n    target: \"\"\n  }, {\n    href: \"/services\",\n    text: \"Services\",\n    target: \"\"\n  }, {\n    href: \"/contact\",\n    text: \"Contact\",\n    target: \"\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    \"data-color\": colorTheme,\n    className: isScrolled ? 'collapsed' : '',\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-background-collapsed\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"left\",\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"logo logo-dark\",\n          src: \"/images/diamond-logo-dark.png\",\n          alt: \"Diamond Atelier\",\n          width: \"120\",\n          height: \"32\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"logo logo-white\",\n          src: \"/images/diamond-logo-white.png\",\n          alt: \"Diamond Atelier\",\n          width: \"120\",\n          height: \"32\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"right hide-mobile\",\n      children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"route-items\",\n        children: navigationItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            target: item.target,\n            children: item.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/underline.png\",\n            alt: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/appointment\",\n        className: \"button-appointment\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"icon icon-calendar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Book Consultation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button button-contact elegant\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/hover-light.png\",\n          className: \"picture-hover\",\n          alt: \"\",\n          loading: \"lazy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/diamond-icon.png\",\n          className: \"button-icon\",\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"right hide-tablet hide-desktop\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `button-menu ${isMobileMenuOpen ? 'active' : ''}`,\n        onClick: onToggleMobileMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"menu menu-dark\",\n            src: \"/images/menu-dark.png\",\n            alt: \"\",\n            width: \"32\",\n            height: \"32\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"menu menu-white\",\n            src: \"/images/cross.png\",\n            alt: \"\",\n            width: \"32\",\n            height: \"32\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"Zu2+HceXPKQDpCQFAQvtMB5fLpA=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Header", "onToggleMobileMenu", "onOpenAppPopup", "isMobileMenuOpen", "_s", "isScrolled", "setIsScrolled", "colorTheme", "handleScroll", "scrolled", "window", "scrollY", "addEventListener", "removeEventListener", "navigationItems", "href", "text", "target", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "width", "height", "map", "item", "index", "loading", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Header/Header.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst Header = ({ onToggleMobileMenu, onOpenAppPopup, isMobileMenuOpen }) => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [colorTheme] = useState('dark');\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrolled = window.scrollY > 50;\n      setIsScrolled(scrolled);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigationItems = [\n    { href: \"/collections\", text: \"Collections\", target: \"\" },\n    { href: \"/custom-design\", text: \"Custom Design\", target: \"\" },\n    { href: \"/about\", text: \"About\", target: \"\" },\n    { href: \"/services\", text: \"Services\", target: \"\" },\n    { href: \"/contact\", text: \"Contact\", target: \"\" }\n  ];\n\n  return (\n    <header \n      data-color={colorTheme} \n      className={isScrolled ? 'collapsed' : ''}\n    >\n      <div className=\"header-background-collapsed\"></div>\n      \n      <div className=\"left\">\n        <a href=\"/\">\n          <img\n            className=\"logo logo-dark\"\n            src=\"/images/diamond-logo-dark.png\"\n            alt=\"Diamond Atelier\"\n            width=\"120\"\n            height=\"32\"\n          />\n          <img\n            className=\"logo logo-white\"\n            src=\"/images/diamond-logo-white.png\"\n            alt=\"Diamond Atelier\"\n            width=\"120\"\n            height=\"32\"\n          />\n        </a>\n      </div>\n\n      <div className=\"right hide-mobile\">\n        <ul className=\"route-items\">\n          {navigationItems.map((item, index) => (\n            <li key={index}>\n              <a href={item.href} target={item.target}>\n                {item.text}\n              </a>\n              <img src=\"/images/underline.png\" alt=\"\" />\n            </li>\n          ))}\n        </ul>\n\n        <a href=\"/appointment\" className=\"button-appointment\">\n          <i className=\"icon icon-calendar\"></i>\n          <span>Book Consultation</span>\n        </a>\n\n        <button className=\"button button-contact elegant\">\n          <img src=\"/images/hover-light.png\" className=\"picture-hover\" alt=\"\" loading=\"lazy\" />\n          <span>Contact Us</span>\n          <img src=\"/images/diamond-icon.png\" className=\"button-icon\" alt=\"\" />\n        </button>\n      </div>\n\n      <div className=\"right hide-tablet hide-desktop\">\n        <div \n          className={`button-menu ${isMobileMenuOpen ? 'active' : ''}`}\n          onClick={onToggleMobileMenu}\n        >\n          <span>Menu</span>\n          <div className=\"relative\">\n            <img \n              className=\"menu menu-dark\" \n              src=\"/images/menu-dark.png\" \n              alt=\"\" \n              width=\"32\" \n              height=\"32\" \n            />\n            <img \n              className=\"menu menu-white\" \n              src=\"/images/cross.png\" \n              alt=\"\" \n              width=\"32\" \n              height=\"32\" \n            />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,kBAAkB;EAAEC,cAAc;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACW,UAAU,CAAC,GAAGX,QAAQ,CAAC,MAAM,CAAC;EAErCC,SAAS,CAAC,MAAM;IACd,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAGC,MAAM,CAACC,OAAO,GAAG,EAAE;MACpCL,aAAa,CAACG,QAAQ,CAAC;IACzB,CAAC;IAEDC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IAC/C,OAAO,MAAME,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,aAAa;IAAEC,MAAM,EAAE;EAAG,CAAC,EACzD;IAAEF,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC7D;IAAEF,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC7C;IAAEF,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,UAAU;IAAEC,MAAM,EAAE;EAAG,CAAC,EACnD;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,SAAS;IAAEC,MAAM,EAAE;EAAG,CAAC,CAClD;EAED,oBACElB,OAAA;IACE,cAAYQ,UAAW;IACvBW,SAAS,EAAEb,UAAU,GAAG,WAAW,GAAG,EAAG;IAAAc,QAAA,gBAEzCpB,OAAA;MAAKmB,SAAS,EAAC;IAA6B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEnDxB,OAAA;MAAKmB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBpB,OAAA;QAAGgB,IAAI,EAAC,GAAG;QAAAI,QAAA,gBACTpB,OAAA;UACEmB,SAAS,EAAC,gBAAgB;UAC1BM,GAAG,EAAC,+BAA+B;UACnCC,GAAG,EAAC,iBAAiB;UACrBC,KAAK,EAAC,KAAK;UACXC,MAAM,EAAC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACFxB,OAAA;UACEmB,SAAS,EAAC,iBAAiB;UAC3BM,GAAG,EAAC,gCAAgC;UACpCC,GAAG,EAAC,iBAAiB;UACrBC,KAAK,EAAC,KAAK;UACXC,MAAM,EAAC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENxB,OAAA;MAAKmB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpB,OAAA;QAAImB,SAAS,EAAC,aAAa;QAAAC,QAAA,EACxBL,eAAe,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/B/B,OAAA;UAAAoB,QAAA,gBACEpB,OAAA;YAAGgB,IAAI,EAAEc,IAAI,CAACd,IAAK;YAACE,MAAM,EAAEY,IAAI,CAACZ,MAAO;YAAAE,QAAA,EACrCU,IAAI,CAACb;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACJxB,OAAA;YAAKyB,GAAG,EAAC,uBAAuB;YAACC,GAAG,EAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAJnCO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAELxB,OAAA;QAAGgB,IAAI,EAAC,cAAc;QAACG,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACnDpB,OAAA;UAAGmB,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCxB,OAAA;UAAAoB,QAAA,EAAM;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEJxB,OAAA;QAAQmB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC/CpB,OAAA;UAAKyB,GAAG,EAAC,yBAAyB;UAACN,SAAS,EAAC,eAAe;UAACO,GAAG,EAAC,EAAE;UAACM,OAAO,EAAC;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrFxB,OAAA;UAAAoB,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvBxB,OAAA;UAAKyB,GAAG,EAAC,0BAA0B;UAACN,SAAS,EAAC,aAAa;UAACO,GAAG,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENxB,OAAA;MAAKmB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CpB,OAAA;QACEmB,SAAS,EAAE,eAAef,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC7D6B,OAAO,EAAE/B,kBAAmB;QAAAkB,QAAA,gBAE5BpB,OAAA;UAAAoB,QAAA,EAAM;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjBxB,OAAA;UAAKmB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpB,OAAA;YACEmB,SAAS,EAAC,gBAAgB;YAC1BM,GAAG,EAAC,uBAAuB;YAC3BC,GAAG,EAAC,EAAE;YACNC,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACFxB,OAAA;YACEmB,SAAS,EAAC,iBAAiB;YAC3BM,GAAG,EAAC,mBAAmB;YACvBC,GAAG,EAAC,EAAE;YACNC,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACnB,EAAA,CAlGIJ,MAAM;AAAAiC,EAAA,GAANjC,MAAM;AAoGZ,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}