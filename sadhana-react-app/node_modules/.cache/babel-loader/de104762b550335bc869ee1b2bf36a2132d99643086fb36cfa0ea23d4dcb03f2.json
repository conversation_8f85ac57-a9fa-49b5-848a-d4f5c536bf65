{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Footer/Footer.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = ({\n  onOpenAppPopup\n}) => {\n  const footerLinks = [{\n    href: \"/terms\",\n    text: \"Terms of Service\",\n    target: \"\"\n  }, {\n    href: \"/privacy\",\n    text: \"Privacy Policy\",\n    target: \"\"\n  }, {\n    href: \"/warranty\",\n    text: \"Lifetime Warranty\",\n    target: \"\"\n  }, {\n    href: \"/care-guide\",\n    text: \"Jewelry Care\",\n    target: \"\"\n  }, {\n    href: \"/about\",\n    text: \"About Us\",\n    target: \"\"\n  }];\n  const socialLinks = [{\n    href: \"https://twitter.com/DiamondAtelier\",\n    icon: \"icon-twitter\",\n    target: \"_blank\"\n  }, {\n    href: \"https://www.instagram.com/diamond_atelier_official/\",\n    icon: \"icon-instagram\",\n    target: \"_blank\"\n  }, {\n    href: \"https://www.facebook.com/DiamondAtelier\",\n    icon: \"icon-facebook\",\n    target: \"_blank\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"footer-wrapper\",\n    className: \"page-size-scrolling-unit\",\n    children: /*#__PURE__*/_jsxDEV(\"footer\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"emblem\",\n          src: \"/images/emblem.png\",\n          alt: \"\",\n          width: \"219\",\n          height: \"209\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"get-app\",\n          onClick: onOpenAppPopup,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"icon icon-long-arrow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Get the app\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"icon icon-long-arrow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"applications\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            className: \"item\",\n            href: \"https://play.google.com/store/apps/details?id=net.vsf.sadhana\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/images/android.png\",\n                alt: \"android\",\n                width: \"105\",\n                height: \"105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"ANDROID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            className: \"item\",\n            href: \"https://apps.apple.com/app/sadhana-mantra-yagna/id1584307762\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/images/ios.png\",\n                alt: \"ios\",\n                width: \"105\",\n                height: \"105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"iOS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"bottom-links\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '650px'\n          },\n          children: [footerLinks.map((link, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: link.href,\n              target: link.target,\n              children: link.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"social hide-mobile\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Follow us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: social.href,\n              target: social.target,\n              rel: \"noopener noreferrer\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `icon ${social.icon}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "onOpenAppPopup", "footerLinks", "href", "text", "target", "socialLinks", "icon", "id", "className", "children", "src", "alt", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "rel", "style", "max<PERSON><PERSON><PERSON>", "map", "link", "index", "social", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Footer/Footer.js"], "sourcesContent": ["import React from 'react';\n\nconst Footer = ({ onOpenAppPopup }) => {\n  const footerLinks = [\n    { href: \"/terms\", text: \"Terms of Service\", target: \"\" },\n    { href: \"/privacy\", text: \"Privacy Policy\", target: \"\" },\n    { href: \"/warranty\", text: \"Lifetime Warranty\", target: \"\" },\n    { href: \"/care-guide\", text: \"Jewelry Care\", target: \"\" },\n    { href: \"/about\", text: \"About Us\", target: \"\" }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/DiamondAtelier\",\n      icon: \"icon-twitter\",\n      target: \"_blank\"\n    },\n    {\n      href: \"https://www.instagram.com/diamond_atelier_official/\",\n      icon: \"icon-instagram\",\n      target: \"_blank\"\n    },\n    {\n      href: \"https://www.facebook.com/DiamondAtelier\",\n      icon: \"icon-facebook\",\n      target: \"_blank\"\n    }\n  ];\n\n  return (\n    <div id=\"footer-wrapper\" className=\"page-size-scrolling-unit\">\n      <footer>\n        <div className=\"content\">\n          <img \n            className=\"emblem\" \n            src=\"/images/emblem.png\" \n            alt=\"\" \n            width=\"219\" \n            height=\"209\" \n          />\n          \n          <div className=\"get-app\" onClick={onOpenAppPopup}>\n            <i className=\"icon icon-long-arrow\"></i>\n            <span>Get the app</span>\n            <i className=\"icon icon-long-arrow\"></i>\n          </div>\n\n          <div className=\"applications\">\n            <a \n              className=\"item\" \n              href=\"https://play.google.com/store/apps/details?id=net.vsf.sadhana\" \n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <div>\n                <img src=\"/images/android.png\" alt=\"android\" width=\"105\" height=\"105\" />\n              </div>\n              <span>ANDROID</span>\n            </a>\n            \n            <a \n              className=\"item\" \n              href=\"https://apps.apple.com/app/sadhana-mantra-yagna/id1584307762\" \n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <div>\n                <img src=\"/images/ios.png\" alt=\"ios\" width=\"105\" height=\"105\" />\n              </div>\n              <span>iOS</span>\n            </a>\n          </div>\n        </div>\n\n        <ul className=\"bottom-links\">\n          <div style={{ maxWidth: '650px' }}>\n            {footerLinks.map((link, index) => (\n              <li key={index}>\n                <a href={link.href} target={link.target}>\n                  {link.text}\n                </a>\n              </li>\n            ))}\n            \n            <li className=\"social hide-mobile\">\n              <span>Follow us</span>\n              {socialLinks.map((social, index) => (\n                <a \n                  key={index}\n                  href={social.href} \n                  target={social.target}\n                  rel=\"noopener noreferrer\"\n                >\n                  <i className={`icon ${social.icon}`}></i>\n                </a>\n              ))}\n            </li>\n          </div>\n        </ul>\n      </footer>\n    </div>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EACrC,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE;EAAG,CAAC,EACxD;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,MAAM,EAAE;EAAG,CAAC,EACxD;IAAEF,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC5D;IAAEF,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAG,CAAC,EACzD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,UAAU;IAAEC,MAAM,EAAE;EAAG,CAAC,CACjD;EAED,MAAMC,WAAW,GAAG,CAClB;IACEH,IAAI,EAAE,oCAAoC;IAC1CI,IAAI,EAAE,cAAc;IACpBF,MAAM,EAAE;EACV,CAAC,EACD;IACEF,IAAI,EAAE,qDAAqD;IAC3DI,IAAI,EAAE,gBAAgB;IACtBF,MAAM,EAAE;EACV,CAAC,EACD;IACEF,IAAI,EAAE,yCAAyC;IAC/CI,IAAI,EAAE,eAAe;IACrBF,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEN,OAAA;IAAKS,EAAE,EAAC,gBAAgB;IAACC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,eAC3DX,OAAA;MAAAW,QAAA,gBACEX,OAAA;QAAKU,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBX,OAAA;UACEU,SAAS,EAAC,QAAQ;UAClBE,GAAG,EAAC,oBAAoB;UACxBC,GAAG,EAAC,EAAE;UACNC,KAAK,EAAC,KAAK;UACXC,MAAM,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEFnB,OAAA;UAAKU,SAAS,EAAC,SAAS;UAACU,OAAO,EAAElB,cAAe;UAAAS,QAAA,gBAC/CX,OAAA;YAAGU,SAAS,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCnB,OAAA;YAAAW,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxBnB,OAAA;YAAGU,SAAS,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAENnB,OAAA;UAAKU,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BX,OAAA;YACEU,SAAS,EAAC,MAAM;YAChBN,IAAI,EAAC,+DAA+D;YACpEE,MAAM,EAAC,QAAQ;YACfe,GAAG,EAAC,qBAAqB;YAAAV,QAAA,gBAEzBX,OAAA;cAAAW,QAAA,eACEX,OAAA;gBAAKY,GAAG,EAAC,qBAAqB;gBAACC,GAAG,EAAC,SAAS;gBAACC,KAAK,EAAC,KAAK;gBAACC,MAAM,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACNnB,OAAA;cAAAW,QAAA,EAAM;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAEJnB,OAAA;YACEU,SAAS,EAAC,MAAM;YAChBN,IAAI,EAAC,8DAA8D;YACnEE,MAAM,EAAC,QAAQ;YACfe,GAAG,EAAC,qBAAqB;YAAAV,QAAA,gBAEzBX,OAAA;cAAAW,QAAA,eACEX,OAAA;gBAAKY,GAAG,EAAC,iBAAiB;gBAACC,GAAG,EAAC,KAAK;gBAACC,KAAK,EAAC,KAAK;gBAACC,MAAM,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNnB,OAAA;cAAAW,QAAA,EAAM;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAIU,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC1BX,OAAA;UAAKsB,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ,CAAE;UAAAZ,QAAA,GAC/BR,WAAW,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3B1B,OAAA;YAAAW,QAAA,eACEX,OAAA;cAAGI,IAAI,EAAEqB,IAAI,CAACrB,IAAK;cAACE,MAAM,EAAEmB,IAAI,CAACnB,MAAO;cAAAK,QAAA,EACrCc,IAAI,CAACpB;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC,GAHGO,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACL,CAAC,eAEFnB,OAAA;YAAIU,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAChCX,OAAA;cAAAW,QAAA,EAAM;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrBZ,WAAW,CAACiB,GAAG,CAAC,CAACG,MAAM,EAAED,KAAK,kBAC7B1B,OAAA;cAEEI,IAAI,EAAEuB,MAAM,CAACvB,IAAK;cAClBE,MAAM,EAAEqB,MAAM,CAACrB,MAAO;cACtBe,GAAG,EAAC,qBAAqB;cAAAV,QAAA,eAEzBX,OAAA;gBAAGU,SAAS,EAAE,QAAQiB,MAAM,CAACnB,IAAI;cAAG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC,GALpCO,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMT,CACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACS,EAAA,GApGI3B,MAAM;AAsGZ,eAAeA,MAAM;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}