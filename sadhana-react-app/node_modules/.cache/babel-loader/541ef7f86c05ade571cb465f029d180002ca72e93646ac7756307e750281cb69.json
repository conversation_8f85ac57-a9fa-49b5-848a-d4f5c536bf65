{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Homepage/Homepage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport WelcomeSlide from '../Slides/WelcomeSlide';\nimport AwakenSlide from '../Slides/AwakenSlide';\nimport ReturnSlide from '../Slides/ReturnSlide';\nimport PossibleSlide from '../Slides/PossibleSlide';\nimport FooterSlide from '../Slides/FooterSlide';\nimport useSlideNavigation from '../../hooks/useSlideNavigation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Homepage = ({\n  onOpenAppPopup\n}) => {\n  _s();\n  const {\n    currentSlide,\n    goToSlide\n  } = useSlideNavigation(5);\n  const slides = [{\n    component: WelcomeSlide,\n    background: \"image:home1;color:true;center:0.35,0.5;position:-0.1,0\"\n  }, {\n    component: AwakenSlide,\n    background: \"image:home2;color:true;center:0.8,0.5\"\n  }, {\n    component: ReturnSlide,\n    background: \"image:home3;color:true;center:0.35,0.5\"\n  }, {\n    component: PossibleSlide,\n    background: \"image:home4;color:true;center:0.75,0.5\"\n  }, {\n    component: FooterSlide,\n    background: \"\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homepage\",\n    children: [slides.map((slide, index) => {\n      const SlideComponent = slide.component;\n      const isActive = index === currentSlide;\n      const slideDirection = index < currentSlide ? 'pageslide-up' : 'pageslide-down';\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${!isActive ? slideDirection : ''}`,\n        \"data-gl-background\": slide.background,\n        style: {\n          zIndex: isActive ? 10 : 1,\n          opacity: isActive ? 1 : 0,\n          transform: isActive ? 'translateY(0)' : index < currentSlide ? 'translateY(-100vh)' : 'translateY(100vh)',\n          backgroundColor: index === 4 ? '#51301d' : 'transparent' // Footer slide background\n        },\n        children: /*#__PURE__*/_jsxDEV(SlideComponent, {\n          isActive: isActive,\n          onOpenAppPopup: onOpenAppPopup\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-indicators\",\n      children: slides.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `slide-indicator ${index === currentSlide ? 'active' : ''}`,\n        onClick: () => goToSlide(index),\n        title: `Go to slide ${index + 1}`\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_s(Homepage, \"/qio3LyGzkG7vna9UDXAIH9YxJk=\", false, function () {\n  return [useSlideNavigation];\n});\n_c = Homepage;\nexport default Homepage;\nvar _c;\n$RefreshReg$(_c, \"Homepage\");", "map": {"version": 3, "names": ["React", "WelcomeSlide", "AwakenSlide", "ReturnSlide", "PossibleSlide", "FooterSlide", "useSlideNavigation", "jsxDEV", "_jsxDEV", "Homepage", "onOpenAppPopup", "_s", "currentSlide", "goToSlide", "slides", "component", "background", "className", "children", "map", "slide", "index", "SlideComponent", "isActive", "slideDirection", "style", "zIndex", "opacity", "transform", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Homepage/Homepage.js"], "sourcesContent": ["import React from 'react';\nimport WelcomeSlide from '../Slides/WelcomeSlide';\nimport AwakenSlide from '../Slides/AwakenSlide';\nimport ReturnSlide from '../Slides/ReturnSlide';\nimport PossibleSlide from '../Slides/PossibleSlide';\nimport FooterSlide from '../Slides/FooterSlide';\nimport useSlideNavigation from '../../hooks/useSlideNavigation';\n\nconst Homepage = ({ onOpenAppPopup }) => {\n  const { currentSlide, goToSlide } = useSlideNavigation(5);\n\n  const slides = [\n    { component: WelcomeSlide, background: \"image:home1;color:true;center:0.35,0.5;position:-0.1,0\" },\n    { component: AwakenSlide, background: \"image:home2;color:true;center:0.8,0.5\" },\n    { component: ReturnSlide, background: \"image:home3;color:true;center:0.35,0.5\" },\n    { component: PossibleSlide, background: \"image:home4;color:true;center:0.75,0.5\" },\n    { component: FooterSlide, background: \"\" }\n  ];\n\n  return (\n    <div className=\"homepage\">\n      {slides.map((slide, index) => {\n        const SlideComponent = slide.component;\n        const isActive = index === currentSlide;\n        const slideDirection = index < currentSlide ? 'pageslide-up' : 'pageslide-down';\n\n        return (\n          <div\n            key={index}\n            className={`fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${!isActive ? slideDirection : ''}`}\n            data-gl-background={slide.background}\n            style={{\n              zIndex: isActive ? 10 : 1,\n              opacity: isActive ? 1 : 0,\n              transform: isActive ? 'translateY(0)' :\n                        index < currentSlide ? 'translateY(-100vh)' : 'translateY(100vh)',\n              backgroundColor: index === 4 ? '#51301d' : 'transparent' // Footer slide background\n            }}\n          >\n            <SlideComponent\n              isActive={isActive}\n              onOpenAppPopup={onOpenAppPopup}\n            />\n          </div>\n        );\n      })}\n\n      {/* Slide Indicators */}\n      <div className=\"slide-indicators\">\n        {slides.map((_, index) => (\n          <div\n            key={index}\n            className={`slide-indicator ${index === currentSlide ? 'active' : ''}`}\n            onClick={() => goToSlide(index)}\n            title={`Go to slide ${index + 1}`}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default Homepage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,kBAAkB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,YAAY;IAAEC;EAAU,CAAC,GAAGP,kBAAkB,CAAC,CAAC,CAAC;EAEzD,MAAMQ,MAAM,GAAG,CACb;IAAEC,SAAS,EAAEd,YAAY;IAAEe,UAAU,EAAE;EAAyD,CAAC,EACjG;IAAED,SAAS,EAAEb,WAAW;IAAEc,UAAU,EAAE;EAAwC,CAAC,EAC/E;IAAED,SAAS,EAAEZ,WAAW;IAAEa,UAAU,EAAE;EAAyC,CAAC,EAChF;IAAED,SAAS,EAAEX,aAAa;IAAEY,UAAU,EAAE;EAAyC,CAAC,EAClF;IAAED,SAAS,EAAEV,WAAW;IAAEW,UAAU,EAAE;EAAG,CAAC,CAC3C;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,UAAU;IAAAC,QAAA,GACtBJ,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MAC5B,MAAMC,cAAc,GAAGF,KAAK,CAACL,SAAS;MACtC,MAAMQ,QAAQ,GAAGF,KAAK,KAAKT,YAAY;MACvC,MAAMY,cAAc,GAAGH,KAAK,GAAGT,YAAY,GAAG,cAAc,GAAG,gBAAgB;MAE/E,oBACEJ,OAAA;QAEES,SAAS,EAAE,kBAAkBI,KAAK,GAAG,CAAC,cAAcE,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAI,CAACA,QAAQ,GAAGC,cAAc,GAAG,EAAE,EAAG;QAClH,sBAAoBJ,KAAK,CAACJ,UAAW;QACrCS,KAAK,EAAE;UACLC,MAAM,EAAEH,QAAQ,GAAG,EAAE,GAAG,CAAC;UACzBI,OAAO,EAAEJ,QAAQ,GAAG,CAAC,GAAG,CAAC;UACzBK,SAAS,EAAEL,QAAQ,GAAG,eAAe,GAC3BF,KAAK,GAAGT,YAAY,GAAG,oBAAoB,GAAG,mBAAmB;UAC3EiB,eAAe,EAAER,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,aAAa,CAAC;QAC3D,CAAE;QAAAH,QAAA,eAEFV,OAAA,CAACc,cAAc;UACbC,QAAQ,EAAEA,QAAS;UACnBb,cAAc,EAAEA;QAAe;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC,GAdGZ,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeP,CAAC;IAEV,CAAC,CAAC,eAGFzB,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BJ,MAAM,CAACK,GAAG,CAAC,CAACe,CAAC,EAAEb,KAAK,kBACnBb,OAAA;QAEES,SAAS,EAAE,mBAAmBI,KAAK,KAAKT,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;QACvEuB,OAAO,EAAEA,CAAA,KAAMtB,SAAS,CAACQ,KAAK,CAAE;QAChCe,KAAK,EAAE,eAAef,KAAK,GAAG,CAAC;MAAG,GAH7BA,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CApDIF,QAAQ;EAAA,QACwBH,kBAAkB;AAAA;AAAA+B,EAAA,GADlD5B,QAAQ;AAsDd,eAAeA,QAAQ;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}