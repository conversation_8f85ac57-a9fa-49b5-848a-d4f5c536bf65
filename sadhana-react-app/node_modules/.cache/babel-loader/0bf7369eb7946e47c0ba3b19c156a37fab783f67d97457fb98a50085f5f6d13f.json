{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/FooterSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FooterSlide = ({\n  isActive\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = FooterSlide;\nexport default FooterSlide;\nvar _c;\n$RefreshReg$(_c, \"FooterSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FooterSlide", "isActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/FooterSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst FooterSlide = ({ isActive }) => {\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        {/* This slide is just for the footer background */}\n      </div>\n    </div>\n  );\n};\n\nexport default FooterSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACpC,oBACEF,OAAA;IAAKG,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BJ,OAAA;MAAKG,SAAS,EAAC;IAAa;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEvB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GARIR,WAAW;AAUjB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}