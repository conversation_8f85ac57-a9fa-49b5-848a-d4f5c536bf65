{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { animateTextIn, splitLetterAnimation } from '../../utils/animations';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeSlide = ({\n  isActive,\n  onOpenAppPopup\n}) => {\n  _s();\n  const titleRef = useRef(null);\n  const textRef = useRef(null);\n  useEffect(() => {\n    if (isActive && titleRef.current && textRef.current) {\n      // Animate title with letter splitting effect\n      splitLetterAnimation(titleRef.current);\n\n      // Animate text content\n      animateTextIn(textRef.current, 600);\n    }\n  }, [isActive]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        ref: titleRef,\n        className: \"fs-h1\",\n        \"data-component\": \"splitLetter\",\n        children: \"Welcome to the ancient world of Vedic glory\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: textRef,\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          \"data-component\": \"splitRow\",\n          children: \"Experience the rewards and power of Mantra Sadhana.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"button-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"button button-download brown\",\n            onClick: onOpenAppPopup,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/hover-dark.png\",\n              className: \"picture-hover\",\n              alt: \"\",\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Download the app\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/emblem_1.png\",\n              className: \"button-icon\",\n              alt: \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomeSlide, \"JBPbolXVO2u6NT5Vy0cqIBSRf6k=\");\n_c = WelcomeSlide;\nexport default WelcomeSlide;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSlide\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "animateTextIn", "splitLetterAnimation", "jsxDEV", "_jsxDEV", "WelcomeSlide", "isActive", "onOpenAppPopup", "_s", "titleRef", "textRef", "current", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "alt", "loading", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { animateTextIn, splitLetterAnimation } from '../../utils/animations';\n\nconst WelcomeSlide = ({ isActive, onOpenAppPopup }) => {\n  const titleRef = useRef(null);\n  const textRef = useRef(null);\n\n  useEffect(() => {\n    if (isActive && titleRef.current && textRef.current) {\n      // Animate title with letter splitting effect\n      splitLetterAnimation(titleRef.current);\n\n      // Animate text content\n      animateTextIn(textRef.current, 600);\n    }\n  }, [isActive]);\n\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 \n          ref={titleRef}\n          className=\"fs-h1\" \n          data-component=\"splitLetter\"\n        >\n          Welcome to the ancient world of Vedic glory\n        </h1>\n        \n        <div ref={textRef} className=\"text-wrapper\">\n          <p className=\"fs-labeur\" data-component=\"splitRow\">\n            Experience the rewards and power of Mantra Sadhana.\n          </p>\n          \n          <div className=\"button-wrapper\">\n            <button \n              className=\"button button-download brown\"\n              onClick={onOpenAppPopup}\n            >\n              <img \n                src=\"/images/hover-dark.png\" \n                className=\"picture-hover\" \n                alt=\"\" \n                loading=\"lazy\" \n              />\n              <span>Download the app</span>\n              <img \n                src=\"/images/emblem_1.png\" \n                className=\"button-icon\" \n                alt=\"\" \n              />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomeSlide;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAMC,QAAQ,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMU,OAAO,GAAGV,MAAM,CAAC,IAAI,CAAC;EAE5BD,SAAS,CAAC,MAAM;IACd,IAAIO,QAAQ,IAAIG,QAAQ,CAACE,OAAO,IAAID,OAAO,CAACC,OAAO,EAAE;MACnD;MACAT,oBAAoB,CAACO,QAAQ,CAACE,OAAO,CAAC;;MAEtC;MACAV,aAAa,CAACS,OAAO,CAACC,OAAO,EAAE,GAAG,CAAC;IACrC;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,oBACEF,OAAA;IAAKQ,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BT,OAAA;MAAKQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BT,OAAA;QACEU,GAAG,EAAEL,QAAS;QACdG,SAAS,EAAC,OAAO;QACjB,kBAAe,aAAa;QAAAC,QAAA,EAC7B;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELd,OAAA;QAAKU,GAAG,EAAEJ,OAAQ;QAACE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzCT,OAAA;UAAGQ,SAAS,EAAC,WAAW;UAAC,kBAAe,UAAU;UAAAC,QAAA,EAAC;QAEnD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJd,OAAA;UAAKQ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BT,OAAA;YACEQ,SAAS,EAAC,8BAA8B;YACxCO,OAAO,EAAEZ,cAAe;YAAAM,QAAA,gBAExBT,OAAA;cACEgB,GAAG,EAAC,wBAAwB;cAC5BR,SAAS,EAAC,eAAe;cACzBS,GAAG,EAAC,EAAE;cACNC,OAAO,EAAC;YAAM;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFd,OAAA;cAAAS,QAAA,EAAM;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7Bd,OAAA;cACEgB,GAAG,EAAC,sBAAsB;cAC1BR,SAAS,EAAC,aAAa;cACvBS,GAAG,EAAC;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CArDIH,YAAY;AAAAkB,EAAA,GAAZlB,YAAY;AAuDlB,eAAeA,YAAY;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}