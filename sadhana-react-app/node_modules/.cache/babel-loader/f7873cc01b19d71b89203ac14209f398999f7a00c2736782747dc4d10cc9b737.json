{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Homepage/Homepage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport WelcomeSlide from '../Slides/WelcomeSlide';\nimport AwakenSlide from '../Slides/AwakenSlide';\nimport ReturnSlide from '../Slides/ReturnSlide';\nimport PossibleSlide from '../Slides/PossibleSlide';\nimport FooterSlide from '../Slides/FooterSlide';\nimport useSlideNavigation from '../../hooks/useSlideNavigation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Homepage = ({\n  onOpenAppPopup\n}) => {\n  _s();\n  const {\n    currentSlide\n  } = useSlideNavigation(5);\n  const slides = [{\n    component: WelcomeSlide,\n    background: \"image:home1;color:true;center:0.35,0.5;position:-0.1,0\"\n  }, {\n    component: AwakenSlide,\n    background: \"image:home2;color:true;center:0.8,0.5\"\n  }, {\n    component: ReturnSlide,\n    background: \"image:home3;color:true;center:0.35,0.5\"\n  }, {\n    component: PossibleSlide,\n    background: \"image:home4;color:true;center:0.75,0.5\"\n  }, {\n    component: FooterSlide,\n    background: \"\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"root\",\n    className: \"content lang-en_US\",\n    children: /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"page homepage relative\",\n      \"data-component\": \"Homepage\",\n      \"data-interfacescrollposition\": \"right\",\n      \"data-gl-background-global\": \"lens:1;paperA:1\",\n      \"data-postid\": \"273\",\n      children: slides.map((slide, index) => {\n        const SlideComponent = slide.component;\n        const isActive = index === currentSlide;\n        const slideDirection = index < currentSlide ? 'pageslide-left' : 'pageslide-right';\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${slideDirection}`,\n          \"data-gl-background\": slide.background,\n          style: {\n            zIndex: isActive ? 10 : 1,\n            opacity: isActive ? 1 : 0,\n            transform: isActive ? 'translateX(0)' : index < currentSlide ? 'translateX(-100px)' : 'translateX(100px)'\n          },\n          children: /*#__PURE__*/_jsxDEV(SlideComponent, {\n            isActive: isActive,\n            onOpenAppPopup: onOpenAppPopup\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_s(Homepage, \"hpEnM/BIPr0tW++Ey63zx7Pmaiw=\", false, function () {\n  return [useSlideNavigation];\n});\n_c = Homepage;\nexport default Homepage;\nvar _c;\n$RefreshReg$(_c, \"Homepage\");", "map": {"version": 3, "names": ["React", "WelcomeSlide", "AwakenSlide", "ReturnSlide", "PossibleSlide", "FooterSlide", "useSlideNavigation", "jsxDEV", "_jsxDEV", "Homepage", "onOpenAppPopup", "_s", "currentSlide", "slides", "component", "background", "id", "className", "children", "map", "slide", "index", "SlideComponent", "isActive", "slideDirection", "style", "zIndex", "opacity", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Homepage/Homepage.js"], "sourcesContent": ["import React from 'react';\nimport WelcomeSlide from '../Slides/WelcomeSlide';\nimport AwakenSlide from '../Slides/AwakenSlide';\nimport ReturnSlide from '../Slides/ReturnSlide';\nimport PossibleSlide from '../Slides/PossibleSlide';\nimport FooterSlide from '../Slides/FooterSlide';\nimport useSlideNavigation from '../../hooks/useSlideNavigation';\n\nconst Homepage = ({ onOpenAppPopup }) => {\n  const { currentSlide } = useSlideNavigation(5);\n\n  const slides = [\n    { component: WelcomeSlide, background: \"image:home1;color:true;center:0.35,0.5;position:-0.1,0\" },\n    { component: AwakenSlide, background: \"image:home2;color:true;center:0.8,0.5\" },\n    { component: ReturnSlide, background: \"image:home3;color:true;center:0.35,0.5\" },\n    { component: PossibleSlide, background: \"image:home4;color:true;center:0.75,0.5\" },\n    { component: FooterSlide, background: \"\" }\n  ];\n\n  return (\n    <div id=\"root\" className=\"content lang-en_US\">\n      <section \n        className=\"page homepage relative\" \n        data-component=\"Homepage\" \n        data-interfacescrollposition=\"right\" \n        data-gl-background-global=\"lens:1;paperA:1\" \n        data-postid=\"273\"\n      >\n        {slides.map((slide, index) => {\n          const SlideComponent = slide.component;\n          const isActive = index === currentSlide;\n          const slideDirection = index < currentSlide ? 'pageslide-left' : 'pageslide-right';\n\n          return (\n            <div\n              key={index}\n              className={`fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${slideDirection}`}\n              data-gl-background={slide.background}\n              style={{\n                zIndex: isActive ? 10 : 1,\n                opacity: isActive ? 1 : 0,\n                transform: isActive ? 'translateX(0)' :\n                          index < currentSlide ? 'translateX(-100px)' : 'translateX(100px)'\n              }}\n            >\n              <SlideComponent\n                isActive={isActive}\n                onOpenAppPopup={onOpenAppPopup}\n              />\n            </div>\n          );\n        })}\n      </section>\n    </div>\n  );\n};\n\nexport default Homepage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,kBAAkB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAa,CAAC,GAAGN,kBAAkB,CAAC,CAAC,CAAC;EAE9C,MAAMO,MAAM,GAAG,CACb;IAAEC,SAAS,EAAEb,YAAY;IAAEc,UAAU,EAAE;EAAyD,CAAC,EACjG;IAAED,SAAS,EAAEZ,WAAW;IAAEa,UAAU,EAAE;EAAwC,CAAC,EAC/E;IAAED,SAAS,EAAEX,WAAW;IAAEY,UAAU,EAAE;EAAyC,CAAC,EAChF;IAAED,SAAS,EAAEV,aAAa;IAAEW,UAAU,EAAE;EAAyC,CAAC,EAClF;IAAED,SAAS,EAAET,WAAW;IAAEU,UAAU,EAAE;EAAG,CAAC,CAC3C;EAED,oBACEP,OAAA;IAAKQ,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eAC3CV,OAAA;MACES,SAAS,EAAC,wBAAwB;MAClC,kBAAe,UAAU;MACzB,gCAA6B,OAAO;MACpC,6BAA0B,iBAAiB;MAC3C,eAAY,KAAK;MAAAC,QAAA,EAEhBL,MAAM,CAACM,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC5B,MAAMC,cAAc,GAAGF,KAAK,CAACN,SAAS;QACtC,MAAMS,QAAQ,GAAGF,KAAK,KAAKT,YAAY;QACvC,MAAMY,cAAc,GAAGH,KAAK,GAAGT,YAAY,GAAG,gBAAgB,GAAG,iBAAiB;QAElF,oBACEJ,OAAA;UAEES,SAAS,EAAE,kBAAkBI,KAAK,GAAG,CAAC,cAAcE,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAIC,cAAc,EAAG;UACjG,sBAAoBJ,KAAK,CAACL,UAAW;UACrCU,KAAK,EAAE;YACLC,MAAM,EAAEH,QAAQ,GAAG,EAAE,GAAG,CAAC;YACzBI,OAAO,EAAEJ,QAAQ,GAAG,CAAC,GAAG,CAAC;YACzBK,SAAS,EAAEL,QAAQ,GAAG,eAAe,GAC3BF,KAAK,GAAGT,YAAY,GAAG,oBAAoB,GAAG;UAC1D,CAAE;UAAAM,QAAA,eAEFV,OAAA,CAACc,cAAc;YACbC,QAAQ,EAAEA,QAAS;YACnBb,cAAc,EAAEA;UAAe;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC,GAbGX,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcP,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACrB,EAAA,CA/CIF,QAAQ;EAAA,QACaH,kBAAkB;AAAA;AAAA2B,EAAA,GADvCxB,QAAQ;AAiDd,eAAeA,QAAQ;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}