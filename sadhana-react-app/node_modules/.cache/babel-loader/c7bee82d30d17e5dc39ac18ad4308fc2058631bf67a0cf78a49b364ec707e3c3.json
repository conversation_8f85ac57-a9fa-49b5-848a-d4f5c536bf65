{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/ReturnSlide.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { animateTextIn, splitLetterAnimation } from '../../utils/animations';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReturnSlide = ({\n  isActive\n}) => {\n  _s();\n  const titleRef = useRef(null);\n  const textRef = useRef(null);\n  useEffect(() => {\n    if (isActive && titleRef.current && textRef.current) {\n      // Animate title with letter splitting effect\n      splitLetterAnimation(titleRef.current);\n\n      // Animate text content\n      animateTextIn(textRef.current, 600);\n    }\n  }, [isActive]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        ref: titleRef,\n        className: \"fs-h1\",\n        \"data-component\": \"splitLetter\",\n        children: \"Return to your glorious past\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: textRef,\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          \"data-component\": \"splitRow\",\n          children: \"Discover perfect inner balance and harmony with the path of the Vedas.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/?page_id=718\",\n          target: \"\",\n          className: \"link\",\n          children: [\"READ MORE\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"underline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(ReturnSlide, \"JBPbolXVO2u6NT5Vy0cqIBSRf6k=\");\n_c = ReturnSlide;\nexport default ReturnSlide;\nvar _c;\n$RefreshReg$(_c, \"ReturnSlide\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "animateTextIn", "splitLetterAnimation", "jsxDEV", "_jsxDEV", "ReturnSlide", "isActive", "_s", "titleRef", "textRef", "current", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/ReturnSlide.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { animateTextIn, splitLetterAnimation } from '../../utils/animations';\n\nconst ReturnSlide = ({ isActive }) => {\n  const titleRef = useRef(null);\n  const textRef = useRef(null);\n\n  useEffect(() => {\n    if (isActive && titleRef.current && textRef.current) {\n      // Animate title with letter splitting effect\n      splitLetterAnimation(titleRef.current);\n\n      // Animate text content\n      animateTextIn(textRef.current, 600);\n    }\n  }, [isActive]);\n\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 \n          ref={titleRef}\n          className=\"fs-h1\" \n          data-component=\"splitLetter\"\n        >\n          Return to your glorious past\n        </h1>\n        \n        <div ref={textRef} className=\"text-wrapper\">\n          <p className=\"fs-labeur\" data-component=\"splitRow\">\n            Discover perfect inner balance and harmony with the path of the Vedas.\n          </p>\n          \n          <a \n            href=\"/?page_id=718\" \n            target=\"\" \n            className=\"link\"\n          >\n            READ MORE\n            <span className=\"underline\"></span>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReturnSlide;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMS,OAAO,GAAGT,MAAM,CAAC,IAAI,CAAC;EAE5BD,SAAS,CAAC,MAAM;IACd,IAAIO,QAAQ,IAAIE,QAAQ,CAACE,OAAO,IAAID,OAAO,CAACC,OAAO,EAAE;MACnD;MACAR,oBAAoB,CAACM,QAAQ,CAACE,OAAO,CAAC;;MAEtC;MACAT,aAAa,CAACQ,OAAO,CAACC,OAAO,EAAE,GAAG,CAAC;IACrC;EACF,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EAEd,oBACEF,OAAA;IAAKO,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BR,OAAA;MAAKO,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BR,OAAA;QACES,GAAG,EAAEL,QAAS;QACdG,SAAS,EAAC,OAAO;QACjB,kBAAe,aAAa;QAAAC,QAAA,EAC7B;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELb,OAAA;QAAKS,GAAG,EAAEJ,OAAQ;QAACE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzCR,OAAA;UAAGO,SAAS,EAAC,WAAW;UAAC,kBAAe,UAAU;UAAAC,QAAA,EAAC;QAEnD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJb,OAAA;UACEc,IAAI,EAAC,eAAe;UACpBC,MAAM,EAAC,EAAE;UACTR,SAAS,EAAC,MAAM;UAAAC,QAAA,GACjB,WAEC,eAAAR,OAAA;YAAMO,SAAS,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA1CIF,WAAW;AAAAe,EAAA,GAAXf,WAAW;AA4CjB,eAAeA,WAAW;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}