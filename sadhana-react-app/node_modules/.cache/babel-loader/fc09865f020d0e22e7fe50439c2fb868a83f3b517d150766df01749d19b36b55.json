{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeSlide = ({\n  isActive,\n  onOpenAppPopup\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        ref: titleRef,\n        className: \"fs-h1\",\n        \"data-component\": \"splitLetter\",\n        children: \"Welcome to the ancient world of Vedic glory\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: textRef,\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          \"data-component\": \"splitRow\",\n          children: \"Experience the rewards and power of Mantra Sadhana.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"button-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"button button-download brown\",\n            onClick: onOpenAppPopup,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/hover-dark.png\",\n              className: \"picture-hover\",\n              alt: \"\",\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Download the app\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/emblem_1.png\",\n              className: \"button-icon\",\n              alt: \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeSlide;\nexport default WelcomeSlide;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WelcomeSlide", "isActive", "onOpenAppPopup", "className", "children", "ref", "titleRef", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textRef", "onClick", "src", "alt", "loading", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst WelcomeSlide = ({ isActive, onOpenAppPopup }) => {\n\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 \n          ref={titleRef}\n          className=\"fs-h1\" \n          data-component=\"splitLetter\"\n        >\n          Welcome to the ancient world of Vedic glory\n        </h1>\n        \n        <div ref={textRef} className=\"text-wrapper\">\n          <p className=\"fs-labeur\" data-component=\"splitRow\">\n            Experience the rewards and power of Mantra Sadhana.\n          </p>\n          \n          <div className=\"button-wrapper\">\n            <button \n              className=\"button button-download brown\"\n              onClick={onOpenAppPopup}\n            >\n              <img \n                src=\"/images/hover-dark.png\" \n                className=\"picture-hover\" \n                alt=\"\" \n                loading=\"lazy\" \n              />\n              <span>Download the app</span>\n              <img \n                src=\"/images/emblem_1.png\" \n                className=\"button-icon\" \n                alt=\"\" \n              />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomeSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EAErD,oBACEH,OAAA;IAAKI,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BL,OAAA;MAAKI,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BL,OAAA;QACEM,GAAG,EAAEC,QAAS;QACdH,SAAS,EAAC,OAAO;QACjB,kBAAe,aAAa;QAAAC,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELX,OAAA;QAAKM,GAAG,EAAEM,OAAQ;QAACR,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzCL,OAAA;UAAGI,SAAS,EAAC,WAAW;UAAC,kBAAe,UAAU;UAAAC,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJX,OAAA;UAAKI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BL,OAAA;YACEI,SAAS,EAAC,8BAA8B;YACxCS,OAAO,EAAEV,cAAe;YAAAE,QAAA,gBAExBL,OAAA;cACEc,GAAG,EAAC,wBAAwB;cAC5BV,SAAS,EAAC,eAAe;cACzBW,GAAG,EAAC,EAAE;cACNC,OAAO,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFX,OAAA;cAAAK,QAAA,EAAM;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7BX,OAAA;cACEc,GAAG,EAAC,sBAAsB;cAC1BV,SAAS,EAAC,aAAa;cACvBW,GAAG,EAAC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAzCIhB,YAAY;AA2ClB,eAAeA,YAAY;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}