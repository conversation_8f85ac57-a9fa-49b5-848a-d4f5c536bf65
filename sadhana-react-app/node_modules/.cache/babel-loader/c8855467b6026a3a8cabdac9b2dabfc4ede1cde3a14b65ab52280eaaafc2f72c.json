{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeSlide = ({\n  isActive,\n  onOpenAppPopup\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"fs-h1\",\n        children: \"Welcome to the ancient world of Vedic glory\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          children: \"Experience the rewards and power of Mantra Sadhana.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"button-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"button button-download brown\",\n            onClick: onOpenAppPopup,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Download the app\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeSlide;\nexport default WelcomeSlide;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WelcomeSlide", "isActive", "onOpenAppPopup", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst WelcomeSlide = ({ isActive, onOpenAppPopup }) => {\n\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 className=\"fs-h1\">\n          Welcome to the ancient world of Vedic glory\n        </h1>\n\n        <div className=\"text-wrapper\">\n          <p className=\"fs-labeur\">\n            Experience the rewards and power of Mantra Sadhana.\n          </p>\n\n          <div className=\"button-wrapper\">\n            <button\n              className=\"button button-download brown\"\n              onClick={onOpenAppPopup}\n            >\n              <span>Download the app</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomeSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EAErD,oBACEH,OAAA;IAAKI,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BL,OAAA;MAAKI,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BL,OAAA;QAAII,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAC;MAEtB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELT,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BL,OAAA;UAAGI,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJT,OAAA;UAAKI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BL,OAAA;YACEI,SAAS,EAAC,8BAA8B;YACxCM,OAAO,EAAEP,cAAe;YAAAE,QAAA,eAExBL,OAAA;cAAAK,QAAA,EAAM;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA1BIV,YAAY;AA4BlB,eAAeA,YAAY;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}