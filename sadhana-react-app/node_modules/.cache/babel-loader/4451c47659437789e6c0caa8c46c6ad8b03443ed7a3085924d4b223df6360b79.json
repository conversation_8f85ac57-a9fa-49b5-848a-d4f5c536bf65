{"ast": null, "code": "// Animation utility functions\n\nexport const animateTextIn = (element, delay = 0) => {\n  if (!element) return;\n\n  // Reset initial state\n  element.style.opacity = '0';\n  element.style.transform = 'translateY(50px)';\n\n  // Animate in\n  setTimeout(() => {\n    element.style.transition = 'all 0.8s ease';\n    element.style.opacity = '1';\n    element.style.transform = 'translateY(0)';\n  }, delay);\n};\nexport const animateTextOut = element => {\n  if (!element) return;\n  element.style.transition = 'all 0.5s ease';\n  element.style.opacity = '0';\n  element.style.transform = 'translateY(-30px)';\n};\nexport const splitLetterAnimation = element => {\n  if (!element) return;\n  const text = element.textContent;\n  const letters = text.split('').map(letter => letter === ' ' ? '&nbsp;' : `<span class=\"letter\">${letter}</span>`).join('');\n  element.innerHTML = letters;\n  const letterElements = element.querySelectorAll('.letter');\n  letterElements.forEach((letter, index) => {\n    letter.style.opacity = '0';\n    letter.style.transform = 'translateY(50px)';\n    letter.style.transition = 'all 0.6s ease';\n    setTimeout(() => {\n      letter.style.opacity = '1';\n      letter.style.transform = 'translateY(0)';\n    }, index * 50);\n  });\n};\nexport const splitRowAnimation = element => {\n  if (!element) return;\n  const words = element.textContent.split(' ');\n  const rows = [];\n  let currentRow = [];\n\n  // Simple word wrapping simulation\n  words.forEach(word => {\n    currentRow.push(word);\n    if (currentRow.length >= 8) {\n      // Approximate words per row\n      rows.push(currentRow.join(' '));\n      currentRow = [];\n    }\n  });\n  if (currentRow.length > 0) {\n    rows.push(currentRow.join(' '));\n  }\n  const rowElements = rows.map(row => `<span class=\"row\" style=\"display: block; opacity: 0; transform: translateY(30px);\">${row}</span>`).join('');\n  element.innerHTML = rowElements;\n  const rowElementsList = element.querySelectorAll('.row');\n  rowElementsList.forEach((row, index) => {\n    row.style.transition = 'all 0.8s ease';\n    setTimeout(() => {\n      row.style.opacity = '1';\n      row.style.transform = 'translateY(0)';\n    }, index * 200);\n  });\n};\nexport const fadeIn = (element, duration = 500, delay = 0) => {\n  if (!element) return;\n  element.style.opacity = '0';\n  element.style.transition = `opacity ${duration}ms ease`;\n  setTimeout(() => {\n    element.style.opacity = '1';\n  }, delay);\n};\nexport const fadeOut = (element, duration = 500) => {\n  if (!element) return;\n  element.style.transition = `opacity ${duration}ms ease`;\n  element.style.opacity = '0';\n  return new Promise(resolve => {\n    setTimeout(resolve, duration);\n  });\n};\nexport const slideInFromRight = (element, duration = 800, delay = 0) => {\n  if (!element) return;\n  element.style.transform = 'translateX(100px)';\n  element.style.opacity = '0';\n  element.style.transition = `all ${duration}ms ease`;\n  setTimeout(() => {\n    element.style.transform = 'translateX(0)';\n    element.style.opacity = '1';\n  }, delay);\n};\nexport const slideInFromLeft = (element, duration = 800, delay = 0) => {\n  if (!element) return;\n  element.style.transform = 'translateX(-100px)';\n  element.style.opacity = '0';\n  element.style.transition = `all ${duration}ms ease`;\n  setTimeout(() => {\n    element.style.transform = 'translateX(0)';\n    element.style.opacity = '1';\n  }, delay);\n};\nexport const scaleIn = (element, duration = 600, delay = 0) => {\n  if (!element) return;\n  element.style.transform = 'scale(0.8)';\n  element.style.opacity = '0';\n  element.style.transition = `all ${duration}ms ease`;\n  setTimeout(() => {\n    element.style.transform = 'scale(1)';\n    element.style.opacity = '1';\n  }, delay);\n};", "map": {"version": 3, "names": ["animateTextIn", "element", "delay", "style", "opacity", "transform", "setTimeout", "transition", "animateTextOut", "splitLetterAnimation", "text", "textContent", "letters", "split", "map", "letter", "join", "innerHTML", "letterElements", "querySelectorAll", "for<PERSON>ach", "index", "splitRowAnimation", "words", "rows", "currentRow", "word", "push", "length", "rowElements", "row", "rowElementsList", "fadeIn", "duration", "fadeOut", "Promise", "resolve", "slideInFromRight", "slideInFromLeft", "scaleIn"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/utils/animations.js"], "sourcesContent": ["// Animation utility functions\n\nexport const animateTextIn = (element, delay = 0) => {\n  if (!element) return;\n  \n  // Reset initial state\n  element.style.opacity = '0';\n  element.style.transform = 'translateY(50px)';\n  \n  // Animate in\n  setTimeout(() => {\n    element.style.transition = 'all 0.8s ease';\n    element.style.opacity = '1';\n    element.style.transform = 'translateY(0)';\n  }, delay);\n};\n\nexport const animateTextOut = (element) => {\n  if (!element) return;\n  \n  element.style.transition = 'all 0.5s ease';\n  element.style.opacity = '0';\n  element.style.transform = 'translateY(-30px)';\n};\n\nexport const splitLetterAnimation = (element) => {\n  if (!element) return;\n  \n  const text = element.textContent;\n  const letters = text.split('').map(letter => \n    letter === ' ' ? '&nbsp;' : `<span class=\"letter\">${letter}</span>`\n  ).join('');\n  \n  element.innerHTML = letters;\n  \n  const letterElements = element.querySelectorAll('.letter');\n  letterElements.forEach((letter, index) => {\n    letter.style.opacity = '0';\n    letter.style.transform = 'translateY(50px)';\n    letter.style.transition = 'all 0.6s ease';\n    \n    setTimeout(() => {\n      letter.style.opacity = '1';\n      letter.style.transform = 'translateY(0)';\n    }, index * 50);\n  });\n};\n\nexport const splitRowAnimation = (element) => {\n  if (!element) return;\n  \n  const words = element.textContent.split(' ');\n  const rows = [];\n  let currentRow = [];\n  \n  // Simple word wrapping simulation\n  words.forEach(word => {\n    currentRow.push(word);\n    if (currentRow.length >= 8) { // Approximate words per row\n      rows.push(currentRow.join(' '));\n      currentRow = [];\n    }\n  });\n  \n  if (currentRow.length > 0) {\n    rows.push(currentRow.join(' '));\n  }\n  \n  const rowElements = rows.map(row => \n    `<span class=\"row\" style=\"display: block; opacity: 0; transform: translateY(30px);\">${row}</span>`\n  ).join('');\n  \n  element.innerHTML = rowElements;\n  \n  const rowElementsList = element.querySelectorAll('.row');\n  rowElementsList.forEach((row, index) => {\n    row.style.transition = 'all 0.8s ease';\n    \n    setTimeout(() => {\n      row.style.opacity = '1';\n      row.style.transform = 'translateY(0)';\n    }, index * 200);\n  });\n};\n\nexport const fadeIn = (element, duration = 500, delay = 0) => {\n  if (!element) return;\n  \n  element.style.opacity = '0';\n  element.style.transition = `opacity ${duration}ms ease`;\n  \n  setTimeout(() => {\n    element.style.opacity = '1';\n  }, delay);\n};\n\nexport const fadeOut = (element, duration = 500) => {\n  if (!element) return;\n  \n  element.style.transition = `opacity ${duration}ms ease`;\n  element.style.opacity = '0';\n  \n  return new Promise(resolve => {\n    setTimeout(resolve, duration);\n  });\n};\n\nexport const slideInFromRight = (element, duration = 800, delay = 0) => {\n  if (!element) return;\n  \n  element.style.transform = 'translateX(100px)';\n  element.style.opacity = '0';\n  element.style.transition = `all ${duration}ms ease`;\n  \n  setTimeout(() => {\n    element.style.transform = 'translateX(0)';\n    element.style.opacity = '1';\n  }, delay);\n};\n\nexport const slideInFromLeft = (element, duration = 800, delay = 0) => {\n  if (!element) return;\n  \n  element.style.transform = 'translateX(-100px)';\n  element.style.opacity = '0';\n  element.style.transition = `all ${duration}ms ease`;\n  \n  setTimeout(() => {\n    element.style.transform = 'translateX(0)';\n    element.style.opacity = '1';\n  }, delay);\n};\n\nexport const scaleIn = (element, duration = 600, delay = 0) => {\n  if (!element) return;\n  \n  element.style.transform = 'scale(0.8)';\n  element.style.opacity = '0';\n  element.style.transition = `all ${duration}ms ease`;\n  \n  setTimeout(() => {\n    element.style.transform = 'scale(1)';\n    element.style.opacity = '1';\n  }, delay);\n};\n"], "mappings": "AAAA;;AAEA,OAAO,MAAMA,aAAa,GAAGA,CAACC,OAAO,EAAEC,KAAK,GAAG,CAAC,KAAK;EACnD,IAAI,CAACD,OAAO,EAAE;;EAEd;EACAA,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC3BH,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,kBAAkB;;EAE5C;EACAC,UAAU,CAAC,MAAM;IACfL,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,eAAe;IAC1CN,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;IAC3BH,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,eAAe;EAC3C,CAAC,EAAEH,KAAK,CAAC;AACX,CAAC;AAED,OAAO,MAAMM,cAAc,GAAIP,OAAO,IAAK;EACzC,IAAI,CAACA,OAAO,EAAE;EAEdA,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,eAAe;EAC1CN,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC3BH,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,mBAAmB;AAC/C,CAAC;AAED,OAAO,MAAMI,oBAAoB,GAAIR,OAAO,IAAK;EAC/C,IAAI,CAACA,OAAO,EAAE;EAEd,MAAMS,IAAI,GAAGT,OAAO,CAACU,WAAW;EAChC,MAAMC,OAAO,GAAGF,IAAI,CAACG,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAACC,MAAM,IACvCA,MAAM,KAAK,GAAG,GAAG,QAAQ,GAAG,wBAAwBA,MAAM,SAC5D,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAEVf,OAAO,CAACgB,SAAS,GAAGL,OAAO;EAE3B,MAAMM,cAAc,GAAGjB,OAAO,CAACkB,gBAAgB,CAAC,SAAS,CAAC;EAC1DD,cAAc,CAACE,OAAO,CAAC,CAACL,MAAM,EAAEM,KAAK,KAAK;IACxCN,MAAM,CAACZ,KAAK,CAACC,OAAO,GAAG,GAAG;IAC1BW,MAAM,CAACZ,KAAK,CAACE,SAAS,GAAG,kBAAkB;IAC3CU,MAAM,CAACZ,KAAK,CAACI,UAAU,GAAG,eAAe;IAEzCD,UAAU,CAAC,MAAM;MACfS,MAAM,CAACZ,KAAK,CAACC,OAAO,GAAG,GAAG;MAC1BW,MAAM,CAACZ,KAAK,CAACE,SAAS,GAAG,eAAe;IAC1C,CAAC,EAAEgB,KAAK,GAAG,EAAE,CAAC;EAChB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAIrB,OAAO,IAAK;EAC5C,IAAI,CAACA,OAAO,EAAE;EAEd,MAAMsB,KAAK,GAAGtB,OAAO,CAACU,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC;EAC5C,MAAMW,IAAI,GAAG,EAAE;EACf,IAAIC,UAAU,GAAG,EAAE;;EAEnB;EACAF,KAAK,CAACH,OAAO,CAACM,IAAI,IAAI;IACpBD,UAAU,CAACE,IAAI,CAACD,IAAI,CAAC;IACrB,IAAID,UAAU,CAACG,MAAM,IAAI,CAAC,EAAE;MAAE;MAC5BJ,IAAI,CAACG,IAAI,CAACF,UAAU,CAACT,IAAI,CAAC,GAAG,CAAC,CAAC;MAC/BS,UAAU,GAAG,EAAE;IACjB;EACF,CAAC,CAAC;EAEF,IAAIA,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;IACzBJ,IAAI,CAACG,IAAI,CAACF,UAAU,CAACT,IAAI,CAAC,GAAG,CAAC,CAAC;EACjC;EAEA,MAAMa,WAAW,GAAGL,IAAI,CAACV,GAAG,CAACgB,GAAG,IAC9B,sFAAsFA,GAAG,SAC3F,CAAC,CAACd,IAAI,CAAC,EAAE,CAAC;EAEVf,OAAO,CAACgB,SAAS,GAAGY,WAAW;EAE/B,MAAME,eAAe,GAAG9B,OAAO,CAACkB,gBAAgB,CAAC,MAAM,CAAC;EACxDY,eAAe,CAACX,OAAO,CAAC,CAACU,GAAG,EAAET,KAAK,KAAK;IACtCS,GAAG,CAAC3B,KAAK,CAACI,UAAU,GAAG,eAAe;IAEtCD,UAAU,CAAC,MAAM;MACfwB,GAAG,CAAC3B,KAAK,CAACC,OAAO,GAAG,GAAG;MACvB0B,GAAG,CAAC3B,KAAK,CAACE,SAAS,GAAG,eAAe;IACvC,CAAC,EAAEgB,KAAK,GAAG,GAAG,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMW,MAAM,GAAGA,CAAC/B,OAAO,EAAEgC,QAAQ,GAAG,GAAG,EAAE/B,KAAK,GAAG,CAAC,KAAK;EAC5D,IAAI,CAACD,OAAO,EAAE;EAEdA,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC3BH,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,WAAW0B,QAAQ,SAAS;EAEvD3B,UAAU,CAAC,MAAM;IACfL,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC7B,CAAC,EAAEF,KAAK,CAAC;AACX,CAAC;AAED,OAAO,MAAMgC,OAAO,GAAGA,CAACjC,OAAO,EAAEgC,QAAQ,GAAG,GAAG,KAAK;EAClD,IAAI,CAAChC,OAAO,EAAE;EAEdA,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,WAAW0B,QAAQ,SAAS;EACvDhC,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAE3B,OAAO,IAAI+B,OAAO,CAACC,OAAO,IAAI;IAC5B9B,UAAU,CAAC8B,OAAO,EAAEH,QAAQ,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMI,gBAAgB,GAAGA,CAACpC,OAAO,EAAEgC,QAAQ,GAAG,GAAG,EAAE/B,KAAK,GAAG,CAAC,KAAK;EACtE,IAAI,CAACD,OAAO,EAAE;EAEdA,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,mBAAmB;EAC7CJ,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC3BH,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,OAAO0B,QAAQ,SAAS;EAEnD3B,UAAU,CAAC,MAAM;IACfL,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,eAAe;IACzCJ,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC7B,CAAC,EAAEF,KAAK,CAAC;AACX,CAAC;AAED,OAAO,MAAMoC,eAAe,GAAGA,CAACrC,OAAO,EAAEgC,QAAQ,GAAG,GAAG,EAAE/B,KAAK,GAAG,CAAC,KAAK;EACrE,IAAI,CAACD,OAAO,EAAE;EAEdA,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,oBAAoB;EAC9CJ,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC3BH,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,OAAO0B,QAAQ,SAAS;EAEnD3B,UAAU,CAAC,MAAM;IACfL,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,eAAe;IACzCJ,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC7B,CAAC,EAAEF,KAAK,CAAC;AACX,CAAC;AAED,OAAO,MAAMqC,OAAO,GAAGA,CAACtC,OAAO,EAAEgC,QAAQ,GAAG,GAAG,EAAE/B,KAAK,GAAG,CAAC,KAAK;EAC7D,IAAI,CAACD,OAAO,EAAE;EAEdA,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,YAAY;EACtCJ,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC3BH,OAAO,CAACE,KAAK,CAACI,UAAU,GAAG,OAAO0B,QAAQ,SAAS;EAEnD3B,UAAU,CAAC,MAAM;IACfL,OAAO,CAACE,KAAK,CAACE,SAAS,GAAG,UAAU;IACpCJ,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;EAC7B,CAAC,EAAEF,KAAK,CAAC;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}