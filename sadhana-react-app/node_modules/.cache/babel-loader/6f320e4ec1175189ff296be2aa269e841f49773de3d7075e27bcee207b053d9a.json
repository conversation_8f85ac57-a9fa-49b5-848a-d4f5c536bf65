{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeSlide = ({\n  isActive,\n  onOpenAppPopup\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"left-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: [\"Diamond\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 20\n        }, this), \"Atelier\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Crafting exceptional diamonds and\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 45\n        }, this), \"luxury jewelry with timeless elegance.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"slide-navigation\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-arrow\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-arrow\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"right-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"slide-circle\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/diamond-ring.svg\",\n          alt: \"Diamond Ring\",\n          className: \"slide-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeSlide;\nexport default WelcomeSlide;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WelcomeSlide", "isActive", "onOpenAppPopup", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "viewBox", "d", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/WelcomeSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst WelcomeSlide = ({ isActive, onOpenAppPopup }) => {\n  return (\n    <div className=\"layout\">\n      <div className=\"left-content\">\n        <h1>Diamond<br />Atelier</h1>\n        <p>Crafting exceptional diamonds and<br />luxury jewelry with timeless elegance.</p>\n        <div className=\"slide-navigation\">\n          <div className=\"nav-arrow\">\n            <svg viewBox=\"0 0 24 24\">\n              <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n            </svg>\n          </div>\n          <div className=\"nav-arrow\">\n            <svg viewBox=\"0 0 24 24\">\n              <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n            </svg>\n          </div>\n        </div>\n      </div>\n      <div className=\"right-content\">\n        <div className=\"slide-circle\">\n          <img\n            src=\"/images/diamond-ring.svg\"\n            alt=\"Diamond Ring\"\n            className=\"slide-image\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomeSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EACrD,oBACEH,OAAA;IAAKI,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBL,OAAA;MAAKI,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BL,OAAA;QAAAK,QAAA,GAAI,SAAO,eAAAL,OAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,WAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BT,OAAA;QAAAK,QAAA,GAAG,mCAAiC,eAAAL,OAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,0CAAsC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpFT,OAAA;QAAKI,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BL,OAAA;UAAKI,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBL,OAAA;YAAKU,OAAO,EAAC,WAAW;YAAAL,QAAA,eACtBL,OAAA;cAAMW,CAAC,EAAC;YAA+C;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNT,OAAA;UAAKI,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBL,OAAA;YAAKU,OAAO,EAAC,WAAW;YAAAL,QAAA,eACtBL,OAAA;cAAMW,CAAC,EAAC;YAAgD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNT,OAAA;MAAKI,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BL,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BL,OAAA;UACEY,GAAG,EAAC,0BAA0B;UAC9BC,GAAG,EAAC,cAAc;UAClBT,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GA9BIb,YAAY;AAgClB,eAAeA,YAAY;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}