{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nconst useSlideNavigation = (totalSlides = 5) => {\n  _s();\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isScrolling, setIsScrolling] = useState(false);\n  console.log('useSlideNavigation hook - currentSlide:', currentSlide, 'totalSlides:', totalSlides);\n  const goToSlide = useCallback(slideIndex => {\n    if (slideIndex >= 0 && slideIndex < totalSlides && !isScrolling) {\n      setIsScrolling(true);\n      setCurrentSlide(slideIndex);\n      setTimeout(() => {\n        setIsScrolling(false);\n      }, 800);\n    }\n  }, [totalSlides, isScrolling]);\n  const goToNextSlide = useCallback(() => {\n    if (currentSlide < totalSlides - 1) {\n      goToSlide(currentSlide + 1);\n    }\n  }, [currentSlide, totalSlides, goToSlide]);\n  const goToPreviousSlide = useCallback(() => {\n    if (currentSlide > 0) {\n      goToSlide(currentSlide - 1);\n    }\n  }, [currentSlide, goToSlide]);\n  useEffect(() => {\n    const handleWheel = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (isScrolling) return;\n\n      // Add a small threshold to prevent accidental scrolling\n      if (Math.abs(e.deltaY) < 10) return;\n      if (e.deltaY > 0) {\n        goToNextSlide();\n      } else if (e.deltaY < 0) {\n        goToPreviousSlide();\n      }\n    };\n    const handleKeyDown = e => {\n      if (isScrolling) return;\n      switch (e.key) {\n        case 'ArrowDown':\n        case 'PageDown':\n        case ' ':\n          // Space key\n          e.preventDefault();\n          goToNextSlide();\n          break;\n        case 'ArrowUp':\n        case 'PageUp':\n          e.preventDefault();\n          goToPreviousSlide();\n          break;\n        case 'Home':\n          e.preventDefault();\n          goToSlide(0);\n          break;\n        case 'End':\n          e.preventDefault();\n          goToSlide(totalSlides - 1);\n          break;\n        default:\n          break;\n      }\n    };\n    const handleTouchStart = e => {\n      const touch = e.touches[0];\n      window.touchStartY = touch.clientY;\n    };\n    const handleTouchEnd = e => {\n      if (!window.touchStartY || isScrolling) return;\n      const touch = e.changedTouches[0];\n      const touchEndY = touch.clientY;\n      const deltaY = window.touchStartY - touchEndY;\n\n      // Minimum swipe distance\n      if (Math.abs(deltaY) < 50) return;\n      if (deltaY > 0) {\n        goToNextSlide();\n      } else {\n        goToPreviousSlide();\n      }\n      window.touchStartY = null;\n    };\n\n    // Prevent default scroll behavior on the body\n    document.body.style.overflow = 'hidden';\n\n    // Add event listeners\n    window.addEventListener('wheel', handleWheel, {\n      passive: false\n    });\n    window.addEventListener('keydown', handleKeyDown);\n    window.addEventListener('touchstart', handleTouchStart, {\n      passive: true\n    });\n    window.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n    return () => {\n      document.body.style.overflow = 'unset';\n      window.removeEventListener('wheel', handleWheel);\n      window.removeEventListener('keydown', handleKeyDown);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [isScrolling, goToNextSlide, goToPreviousSlide, goToSlide, totalSlides]);\n  return {\n    currentSlide,\n    isScrolling,\n    goToSlide,\n    goToNextSlide,\n    goToPreviousSlide\n  };\n};\n_s(useSlideNavigation, \"SEBbnTbOmyt/dNytWRhpFetBpNE=\");\nexport default useSlideNavigation;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useSlideNavigation", "totalSlides", "_s", "currentSlide", "setCurrentSlide", "isScrolling", "setIsScrolling", "console", "log", "goToSlide", "slideIndex", "setTimeout", "goToNextSlide", "goToPreviousSlide", "handleWheel", "e", "preventDefault", "stopPropagation", "Math", "abs", "deltaY", "handleKeyDown", "key", "handleTouchStart", "touch", "touches", "window", "touchStartY", "clientY", "handleTouchEnd", "changedTouches", "touchEndY", "document", "body", "style", "overflow", "addEventListener", "passive", "removeEventListener"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/hooks/useSlideNavigation.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\n\nconst useSlideNavigation = (totalSlides = 5) => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isScrolling, setIsScrolling] = useState(false);\n\n  console.log('useSlideNavigation hook - currentSlide:', currentSlide, 'totalSlides:', totalSlides);\n\n  const goToSlide = useCallback((slideIndex) => {\n    if (slideIndex >= 0 && slideIndex < totalSlides && !isScrolling) {\n      setIsScrolling(true);\n      setCurrentSlide(slideIndex);\n      \n      setTimeout(() => {\n        setIsScrolling(false);\n      }, 800);\n    }\n  }, [totalSlides, isScrolling]);\n\n  const goToNextSlide = useCallback(() => {\n    if (currentSlide < totalSlides - 1) {\n      goToSlide(currentSlide + 1);\n    }\n  }, [currentSlide, totalSlides, goToSlide]);\n\n  const goToPreviousSlide = useCallback(() => {\n    if (currentSlide > 0) {\n      goToSlide(currentSlide - 1);\n    }\n  }, [currentSlide, goToSlide]);\n\n  useEffect(() => {\n    const handleWheel = (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n\n      if (isScrolling) return;\n\n      // Add a small threshold to prevent accidental scrolling\n      if (Math.abs(e.deltaY) < 10) return;\n\n      if (e.deltaY > 0) {\n        goToNextSlide();\n      } else if (e.deltaY < 0) {\n        goToPreviousSlide();\n      }\n    };\n\n    const handleKeyDown = (e) => {\n      if (isScrolling) return;\n\n      switch (e.key) {\n        case 'ArrowDown':\n        case 'PageDown':\n        case ' ': // Space key\n          e.preventDefault();\n          goToNextSlide();\n          break;\n        case 'ArrowUp':\n        case 'PageUp':\n          e.preventDefault();\n          goToPreviousSlide();\n          break;\n        case 'Home':\n          e.preventDefault();\n          goToSlide(0);\n          break;\n        case 'End':\n          e.preventDefault();\n          goToSlide(totalSlides - 1);\n          break;\n        default:\n          break;\n      }\n    };\n\n    const handleTouchStart = (e) => {\n      const touch = e.touches[0];\n      window.touchStartY = touch.clientY;\n    };\n\n    const handleTouchEnd = (e) => {\n      if (!window.touchStartY || isScrolling) return;\n\n      const touch = e.changedTouches[0];\n      const touchEndY = touch.clientY;\n      const deltaY = window.touchStartY - touchEndY;\n\n      // Minimum swipe distance\n      if (Math.abs(deltaY) < 50) return;\n\n      if (deltaY > 0) {\n        goToNextSlide();\n      } else {\n        goToPreviousSlide();\n      }\n\n      window.touchStartY = null;\n    };\n\n    // Prevent default scroll behavior on the body\n    document.body.style.overflow = 'hidden';\n\n    // Add event listeners\n    window.addEventListener('wheel', handleWheel, { passive: false });\n    window.addEventListener('keydown', handleKeyDown);\n    window.addEventListener('touchstart', handleTouchStart, { passive: true });\n    window.addEventListener('touchend', handleTouchEnd, { passive: true });\n\n    return () => {\n      document.body.style.overflow = 'unset';\n      window.removeEventListener('wheel', handleWheel);\n      window.removeEventListener('keydown', handleKeyDown);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [isScrolling, goToNextSlide, goToPreviousSlide, goToSlide, totalSlides]);\n\n  return {\n    currentSlide,\n    isScrolling,\n    goToSlide,\n    goToNextSlide,\n    goToPreviousSlide\n  };\n};\n\nexport default useSlideNavigation;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAExD,MAAMC,kBAAkB,GAAGA,CAACC,WAAW,GAAG,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACQ,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAErDU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEL,YAAY,EAAE,cAAc,EAAEF,WAAW,CAAC;EAEjG,MAAMQ,SAAS,GAAGV,WAAW,CAAEW,UAAU,IAAK;IAC5C,IAAIA,UAAU,IAAI,CAAC,IAAIA,UAAU,GAAGT,WAAW,IAAI,CAACI,WAAW,EAAE;MAC/DC,cAAc,CAAC,IAAI,CAAC;MACpBF,eAAe,CAACM,UAAU,CAAC;MAE3BC,UAAU,CAAC,MAAM;QACfL,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAACL,WAAW,EAAEI,WAAW,CAAC,CAAC;EAE9B,MAAMO,aAAa,GAAGb,WAAW,CAAC,MAAM;IACtC,IAAII,YAAY,GAAGF,WAAW,GAAG,CAAC,EAAE;MAClCQ,SAAS,CAACN,YAAY,GAAG,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACA,YAAY,EAAEF,WAAW,EAAEQ,SAAS,CAAC,CAAC;EAE1C,MAAMI,iBAAiB,GAAGd,WAAW,CAAC,MAAM;IAC1C,IAAII,YAAY,GAAG,CAAC,EAAE;MACpBM,SAAS,CAACN,YAAY,GAAG,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACA,YAAY,EAAEM,SAAS,CAAC,CAAC;EAE7BX,SAAS,CAAC,MAAM;IACd,MAAMgB,WAAW,GAAIC,CAAC,IAAK;MACzBA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;MAEnB,IAAIZ,WAAW,EAAE;;MAEjB;MACA,IAAIa,IAAI,CAACC,GAAG,CAACJ,CAAC,CAACK,MAAM,CAAC,GAAG,EAAE,EAAE;MAE7B,IAAIL,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;QAChBR,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIG,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;QACvBP,iBAAiB,CAAC,CAAC;MACrB;IACF,CAAC;IAED,MAAMQ,aAAa,GAAIN,CAAC,IAAK;MAC3B,IAAIV,WAAW,EAAE;MAEjB,QAAQU,CAAC,CAACO,GAAG;QACX,KAAK,WAAW;QAChB,KAAK,UAAU;QACf,KAAK,GAAG;UAAE;UACRP,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBJ,aAAa,CAAC,CAAC;UACf;QACF,KAAK,SAAS;QACd,KAAK,QAAQ;UACXG,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBH,iBAAiB,CAAC,CAAC;UACnB;QACF,KAAK,MAAM;UACTE,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBP,SAAS,CAAC,CAAC,CAAC;UACZ;QACF,KAAK,KAAK;UACRM,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBP,SAAS,CAACR,WAAW,GAAG,CAAC,CAAC;UAC1B;QACF;UACE;MACJ;IACF,CAAC;IAED,MAAMsB,gBAAgB,GAAIR,CAAC,IAAK;MAC9B,MAAMS,KAAK,GAAGT,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC;MAC1BC,MAAM,CAACC,WAAW,GAAGH,KAAK,CAACI,OAAO;IACpC,CAAC;IAED,MAAMC,cAAc,GAAId,CAAC,IAAK;MAC5B,IAAI,CAACW,MAAM,CAACC,WAAW,IAAItB,WAAW,EAAE;MAExC,MAAMmB,KAAK,GAAGT,CAAC,CAACe,cAAc,CAAC,CAAC,CAAC;MACjC,MAAMC,SAAS,GAAGP,KAAK,CAACI,OAAO;MAC/B,MAAMR,MAAM,GAAGM,MAAM,CAACC,WAAW,GAAGI,SAAS;;MAE7C;MACA,IAAIb,IAAI,CAACC,GAAG,CAACC,MAAM,CAAC,GAAG,EAAE,EAAE;MAE3B,IAAIA,MAAM,GAAG,CAAC,EAAE;QACdR,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLC,iBAAiB,CAAC,CAAC;MACrB;MAEAa,MAAM,CAACC,WAAW,GAAG,IAAI;IAC3B,CAAC;;IAED;IACAK,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;IAEvC;IACAT,MAAM,CAACU,gBAAgB,CAAC,OAAO,EAAEtB,WAAW,EAAE;MAAEuB,OAAO,EAAE;IAAM,CAAC,CAAC;IACjEX,MAAM,CAACU,gBAAgB,CAAC,SAAS,EAAEf,aAAa,CAAC;IACjDK,MAAM,CAACU,gBAAgB,CAAC,YAAY,EAAEb,gBAAgB,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAC,CAAC;IAC1EX,MAAM,CAACU,gBAAgB,CAAC,UAAU,EAAEP,cAAc,EAAE;MAAEQ,OAAO,EAAE;IAAK,CAAC,CAAC;IAEtE,OAAO,MAAM;MACXL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;MACtCT,MAAM,CAACY,mBAAmB,CAAC,OAAO,EAAExB,WAAW,CAAC;MAChDY,MAAM,CAACY,mBAAmB,CAAC,SAAS,EAAEjB,aAAa,CAAC;MACpDK,MAAM,CAACY,mBAAmB,CAAC,YAAY,EAAEf,gBAAgB,CAAC;MAC1DG,MAAM,CAACY,mBAAmB,CAAC,UAAU,EAAET,cAAc,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACxB,WAAW,EAAEO,aAAa,EAAEC,iBAAiB,EAAEJ,SAAS,EAAER,WAAW,CAAC,CAAC;EAE3E,OAAO;IACLE,YAAY;IACZE,WAAW;IACXI,SAAS;IACTG,aAAa;IACbC;EACF,CAAC;AACH,CAAC;AAACX,EAAA,CA3HIF,kBAAkB;AA6HxB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}