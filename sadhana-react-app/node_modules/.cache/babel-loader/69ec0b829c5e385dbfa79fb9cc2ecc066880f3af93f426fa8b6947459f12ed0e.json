{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/AppDownloadPopup.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppDownloadPopup = ({\n  isOpen,\n  onClose\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"popin-app\",\n    className: `popin ${isOpen ? 'active' : ''}`,\n    onClick: handleBackdropClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/corner.png\",\n        className: \"corner top-left\",\n        width: \"107\",\n        height: \"107\",\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/corner.png\",\n        className: \"corner top-right\",\n        width: \"107\",\n        height: \"107\",\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/corner.png\",\n        className: \"corner bottom-right\",\n        width: \"107\",\n        height: \"107\",\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/corner.png\",\n        className: \"corner bottom-left\",\n        width: \"107\",\n        height: \"107\",\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        className: \"emblem\",\n        src: \"/images/emblem_1.png\",\n        alt: \"\",\n        width: \"38\",\n        height: \"38\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close\",\n        onClick: onClose,\n        children: \"close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-wrapper\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title\",\n            children: \"Get the app\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"applications\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"item\",\n              href: \"https://play.google.com/store/apps/details?id=net.vsf.sadhana\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/images/android.png\",\n                  alt: \"android\",\n                  width: \"105\",\n                  height: \"105\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"ANDROID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"item\",\n              href: \"https://apps.apple.com/app/sadhana-mantra-yagna/id1584307762\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/images/ios.png\",\n                  alt: \"ios\",\n                  width: \"105\",\n                  height: \"105\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"iOS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(AppDownloadPopup, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = AppDownloadPopup;\nexport default AppDownloadPopup;\nvar _c;\n$RefreshReg$(_c, \"AppDownloadPopup\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "AppDownloadPopup", "isOpen", "onClose", "_s", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "handleBackdropClick", "target", "currentTarget", "id", "className", "onClick", "children", "src", "width", "height", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/AppDownloadPopup.js"], "sourcesContent": ["import React, { useEffect } from 'react';\n\nconst AppDownloadPopup = ({ isOpen, onClose }) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div \n      id=\"popin-app\" \n      className={`popin ${isOpen ? 'active' : ''}`}\n      onClick={handleBackdropClick}\n    >\n      <div className=\"content\">\n        <img \n          src=\"/images/corner.png\" \n          className=\"corner top-left\" \n          width=\"107\" \n          height=\"107\" \n          alt=\"\"\n        />\n        <img \n          src=\"/images/corner.png\" \n          className=\"corner top-right\" \n          width=\"107\" \n          height=\"107\" \n          alt=\"\"\n        />\n        <img \n          src=\"/images/corner.png\" \n          className=\"corner bottom-right\" \n          width=\"107\" \n          height=\"107\" \n          alt=\"\"\n        />\n        <img \n          src=\"/images/corner.png\" \n          className=\"corner bottom-left\" \n          width=\"107\" \n          height=\"107\" \n          alt=\"\"\n        />\n        \n        <img \n          className=\"emblem\" \n          src=\"/images/emblem_1.png\" \n          alt=\"\" \n          width=\"38\" \n          height=\"38\" \n        />\n        \n        <button className=\"close\" onClick={onClose}>\n          close\n        </button>\n\n        <div className=\"scroll-wrapper\">\n          <div>\n            <div className=\"title\">Get the app</div>\n            \n            <div className=\"applications\">\n              <a \n                className=\"item\" \n                href=\"https://play.google.com/store/apps/details?id=net.vsf.sadhana\" \n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <div>\n                  <img \n                    src=\"/images/android.png\" \n                    alt=\"android\" \n                    width=\"105\" \n                    height=\"105\" \n                  />\n                </div>\n                <span>ANDROID</span>\n              </a>\n              \n              <a \n                className=\"item\" \n                href=\"https://apps.apple.com/app/sadhana-mantra-yagna/id1584307762\" \n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <div>\n                  <img \n                    src=\"/images/ios.png\" \n                    alt=\"ios\" \n                    width=\"105\" \n                    height=\"105\" \n                  />\n                </div>\n                <span>iOS</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AppDownloadPopup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAChDN,SAAS,CAAC,MAAM;IACd,MAAMO,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVM,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACV,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,MAAMW,mBAAmB,GAAIR,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACS,MAAM,KAAKT,CAAC,CAACU,aAAa,EAAE;MAChCb,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IACEiB,EAAE,EAAC,WAAW;IACdC,SAAS,EAAE,SAAShB,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC7CiB,OAAO,EAAEL,mBAAoB;IAAAM,QAAA,eAE7BpB,OAAA;MAAKkB,SAAS,EAAC,SAAS;MAAAE,QAAA,gBACtBpB,OAAA;QACEqB,GAAG,EAAC,oBAAoB;QACxBH,SAAS,EAAC,iBAAiB;QAC3BI,KAAK,EAAC,KAAK;QACXC,MAAM,EAAC,KAAK;QACZC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACF5B,OAAA;QACEqB,GAAG,EAAC,oBAAoB;QACxBH,SAAS,EAAC,kBAAkB;QAC5BI,KAAK,EAAC,KAAK;QACXC,MAAM,EAAC,KAAK;QACZC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACF5B,OAAA;QACEqB,GAAG,EAAC,oBAAoB;QACxBH,SAAS,EAAC,qBAAqB;QAC/BI,KAAK,EAAC,KAAK;QACXC,MAAM,EAAC,KAAK;QACZC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACF5B,OAAA;QACEqB,GAAG,EAAC,oBAAoB;QACxBH,SAAS,EAAC,oBAAoB;QAC9BI,KAAK,EAAC,KAAK;QACXC,MAAM,EAAC,KAAK;QACZC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEF5B,OAAA;QACEkB,SAAS,EAAC,QAAQ;QAClBG,GAAG,EAAC,sBAAsB;QAC1BG,GAAG,EAAC,EAAE;QACNF,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEF5B,OAAA;QAAQkB,SAAS,EAAC,OAAO;QAACC,OAAO,EAAEhB,OAAQ;QAAAiB,QAAA,EAAC;MAE5C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5B,OAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA;YAAKkB,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAExC5B,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAE,QAAA,gBAC3BpB,OAAA;cACEkB,SAAS,EAAC,MAAM;cAChBW,IAAI,EAAC,+DAA+D;cACpEd,MAAM,EAAC,QAAQ;cACfe,GAAG,EAAC,qBAAqB;cAAAV,QAAA,gBAEzBpB,OAAA;gBAAAoB,QAAA,eACEpB,OAAA;kBACEqB,GAAG,EAAC,qBAAqB;kBACzBG,GAAG,EAAC,SAAS;kBACbF,KAAK,EAAC,KAAK;kBACXC,MAAM,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5B,OAAA;gBAAAoB,QAAA,EAAM;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eAEJ5B,OAAA;cACEkB,SAAS,EAAC,MAAM;cAChBW,IAAI,EAAC,8DAA8D;cACnEd,MAAM,EAAC,QAAQ;cACfe,GAAG,EAAC,qBAAqB;cAAAV,QAAA,gBAEzBpB,OAAA;gBAAAoB,QAAA,eACEpB,OAAA;kBACEqB,GAAG,EAAC,iBAAiB;kBACrBG,GAAG,EAAC,KAAK;kBACTF,KAAK,EAAC,KAAK;kBACXC,MAAM,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5B,OAAA;gBAAAoB,QAAA,EAAM;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAvHIH,gBAAgB;AAAA8B,EAAA,GAAhB9B,gBAAgB;AAyHtB,eAAeA,gBAAgB;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}