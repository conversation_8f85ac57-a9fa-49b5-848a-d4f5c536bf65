{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/PossibleSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PossibleSlide = ({\n  isActive\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"fs-h1\",\n        children: \"It's possible with <PERSON><PERSON>\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          children: \"Created to serve humanity. 100% free. No subscription, no paywall, no restrictions. No advertisements.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/?page_id=52\",\n            target: \"\",\n            className: \"link\",\n            children: [\"Get Involved\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"underline\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = PossibleSlide;\nexport default PossibleSlide;\nvar _c;\n$RefreshReg$(_c, \"PossibleSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PossibleSlide", "isActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/PossibleSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst PossibleSlide = ({ isActive }) => {\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 className=\"fs-h1\">\n          It's possible with Sad<PERSON>\n        </h1>\n\n        <div className=\"text-wrapper\">\n          <p className=\"fs-labeur\">\n            Created to serve humanity. 100% free. No subscription, no paywall, no restrictions. No advertisements.\n          </p>\n\n          <div className=\"cta-wrapper\">\n            <a\n              href=\"/?page_id=52\"\n              target=\"\"\n              className=\"link\"\n            >\n              Get Involved\n              <span className=\"underline\"></span>\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PossibleSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACtC,oBACEF,OAAA;IAAKG,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BJ,OAAA;MAAKG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BJ,OAAA;QAAIG,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAC;MAEtB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELR,OAAA;QAAKG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BJ,OAAA;UAAGG,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA;UAAKG,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BJ,OAAA;YACES,IAAI,EAAC,cAAc;YACnBC,MAAM,EAAC,EAAE;YACTP,SAAS,EAAC,MAAM;YAAAC,QAAA,GACjB,cAEC,eAAAJ,OAAA;cAAMG,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GA3BIV,aAAa;AA6BnB,eAAeA,aAAa;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}