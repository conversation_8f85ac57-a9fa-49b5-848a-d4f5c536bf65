{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/Interface.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Interface = () => {\n  _s();\n  const [isMuted, setIsMuted] = useState(true);\n  const toggleMute = () => {\n    const audio = document.getElementById('background-audio');\n    if (audio) {\n      if (isMuted) {\n        audio.play();\n      } else {\n        audio.pause();\n      }\n      setIsMuted(!isMuted);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"interface\",\n    \"data-scrollposition\": \"right\",\n    \"data-color\": \"dark\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"top-left\",\n      src: \"/images/underline.png\",\n      alt: \"\",\n      loading: \"lazy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"top-left rotated\",\n      src: \"/images/underline.png\",\n      alt: \"\",\n      loading: \"lazy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-to-explore-wrapper bottom-left\",\n      \"data-scrollto\": \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Scroll to explore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"icon icon-losange\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/underline.png\",\n        alt: \"\",\n        loading: \"lazy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"bottom-left rotated\",\n      src: \"/images/underline.png\",\n      alt: \"\",\n      loading: \"lazy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"top-right\",\n      src: \"/images/underline.png\",\n      alt: \"\",\n      loading: \"lazy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"top-right rotated\",\n      src: \"/images/underline.png\",\n      alt: \"\",\n      loading: \"lazy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"bottom-right rotated\",\n      src: \"/images/underline.png\",\n      alt: \"\",\n      loading: \"lazy\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-to-explore-wrapper bottom-right\",\n      \"data-scrollto\": \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Scroll to explore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"icon icon-losange\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/underline.png\",\n        alt: \"\",\n        loading: \"lazy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"button-mute\",\n      className: isMuted ? 'muted' : '',\n      onClick: toggleMute,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(Interface, \"sevb0eqMvxSSpMr5VfQIfRUASK0=\");\n_c = Interface;\nexport default Interface;\nvar _c;\n$RefreshReg$(_c, \"Interface\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Interface", "_s", "isMuted", "setIsMuted", "toggleMute", "audio", "document", "getElementById", "play", "pause", "id", "children", "className", "src", "alt", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/Interface.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Interface = () => {\n  const [isMuted, setIsMuted] = useState(true);\n\n  const toggleMute = () => {\n    const audio = document.getElementById('background-audio');\n    if (audio) {\n      if (isMuted) {\n        audio.play();\n      } else {\n        audio.pause();\n      }\n      setIsMuted(!isMuted);\n    }\n  };\n\n  return (\n    <div id=\"interface\" data-scrollposition=\"right\" data-color=\"dark\">\n      <img \n        className=\"top-left\" \n        src=\"/images/underline.png\" \n        alt=\"\" \n        loading=\"lazy\" \n      />\n      <img \n        className=\"top-left rotated\" \n        src=\"/images/underline.png\" \n        alt=\"\" \n        loading=\"lazy\" \n      />\n      \n      <div className=\"scroll-to-explore-wrapper bottom-left\" data-scrollto=\"\">\n        <span>Scroll to explore</span>\n        <i className=\"icon icon-losange\"></i>\n        <img src=\"/images/underline.png\" alt=\"\" loading=\"lazy\" />\n      </div>\n      \n      <img \n        className=\"bottom-left rotated\" \n        src=\"/images/underline.png\" \n        alt=\"\" \n        loading=\"lazy\" \n      />\n      \n      <img \n        className=\"top-right\" \n        src=\"/images/underline.png\" \n        alt=\"\" \n        loading=\"lazy\" \n      />\n      <img \n        className=\"top-right rotated\" \n        src=\"/images/underline.png\" \n        alt=\"\" \n        loading=\"lazy\" \n      />\n      \n      <img \n        className=\"bottom-right rotated\" \n        src=\"/images/underline.png\" \n        alt=\"\" \n        loading=\"lazy\" \n      />\n      \n      <div className=\"scroll-to-explore-wrapper bottom-right\" data-scrollto=\"\">\n        <span>Scroll to explore</span>\n        <i className=\"icon icon-losange\"></i>\n        <img src=\"/images/underline.png\" alt=\"\" loading=\"lazy\" />\n      </div>\n\n      <div \n        id=\"button-mute\" \n        className={isMuted ? 'muted' : ''} \n        onClick={toggleMute}\n      >\n        <i></i>\n        <i></i>\n        <i></i>\n      </div>\n    </div>\n  );\n};\n\nexport default Interface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IACzD,IAAIF,KAAK,EAAE;MACT,IAAIH,OAAO,EAAE;QACXG,KAAK,CAACG,IAAI,CAAC,CAAC;MACd,CAAC,MAAM;QACLH,KAAK,CAACI,KAAK,CAAC,CAAC;MACf;MACAN,UAAU,CAAC,CAACD,OAAO,CAAC;IACtB;EACF,CAAC;EAED,oBACEH,OAAA;IAAKW,EAAE,EAAC,WAAW;IAAC,uBAAoB,OAAO;IAAC,cAAW,MAAM;IAAAC,QAAA,gBAC/DZ,OAAA;MACEa,SAAS,EAAC,UAAU;MACpBC,GAAG,EAAC,uBAAuB;MAC3BC,GAAG,EAAC,EAAE;MACNC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACFpB,OAAA;MACEa,SAAS,EAAC,kBAAkB;MAC5BC,GAAG,EAAC,uBAAuB;MAC3BC,GAAG,EAAC,EAAE;MACNC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEFpB,OAAA;MAAKa,SAAS,EAAC,uCAAuC;MAAC,iBAAc,EAAE;MAAAD,QAAA,gBACrEZ,OAAA;QAAAY,QAAA,EAAM;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9BpB,OAAA;QAAGa,SAAS,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrCpB,OAAA;QAAKc,GAAG,EAAC,uBAAuB;QAACC,GAAG,EAAC,EAAE;QAACC,OAAO,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAENpB,OAAA;MACEa,SAAS,EAAC,qBAAqB;MAC/BC,GAAG,EAAC,uBAAuB;MAC3BC,GAAG,EAAC,EAAE;MACNC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEFpB,OAAA;MACEa,SAAS,EAAC,WAAW;MACrBC,GAAG,EAAC,uBAAuB;MAC3BC,GAAG,EAAC,EAAE;MACNC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACFpB,OAAA;MACEa,SAAS,EAAC,mBAAmB;MAC7BC,GAAG,EAAC,uBAAuB;MAC3BC,GAAG,EAAC,EAAE;MACNC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEFpB,OAAA;MACEa,SAAS,EAAC,sBAAsB;MAChCC,GAAG,EAAC,uBAAuB;MAC3BC,GAAG,EAAC,EAAE;MACNC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEFpB,OAAA;MAAKa,SAAS,EAAC,wCAAwC;MAAC,iBAAc,EAAE;MAAAD,QAAA,gBACtEZ,OAAA;QAAAY,QAAA,EAAM;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9BpB,OAAA;QAAGa,SAAS,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrCpB,OAAA;QAAKc,GAAG,EAAC,uBAAuB;QAACC,GAAG,EAAC,EAAE;QAACC,OAAO,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAENpB,OAAA;MACEW,EAAE,EAAC,aAAa;MAChBE,SAAS,EAAEV,OAAO,GAAG,OAAO,GAAG,EAAG;MAClCkB,OAAO,EAAEhB,UAAW;MAAAO,QAAA,gBAEpBZ,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPpB,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPpB,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CAhFID,SAAS;AAAAqB,EAAA,GAATrB,SAAS;AAkFf,eAAeA,SAAS;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}