{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/FooterSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FooterSlide = ({\n  isActive,\n  onOpenAppPopup\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"fs-h1\",\n        style: {\n          color: 'white'\n        },\n        children: \"Get the App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          style: {\n            color: 'rgba(255, 255, 255, 0.9)'\n          },\n          children: \"Download the Sadhana app and start your spiritual journey today.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"button-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"button button-download brown\",\n            onClick: onOpenAppPopup,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Download Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = FooterSlide;\nexport default FooterSlide;\nvar _c;\n$RefreshReg$(_c, \"FooterSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FooterSlide", "isActive", "onOpenAppPopup", "className", "children", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/FooterSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst FooterSlide = ({ isActive, onOpenAppPopup }) => {\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 className=\"fs-h1\" style={{ color: 'white' }}>\n          Get the App\n        </h1>\n\n        <div className=\"text-wrapper\">\n          <p className=\"fs-labeur\" style={{ color: 'rgba(255, 255, 255, 0.9)' }}>\n            Download the Sadhana app and start your spiritual journey today.\n          </p>\n\n          <div className=\"button-wrapper\">\n            <button\n              className=\"button button-download brown\"\n              onClick={onOpenAppPopup}\n            >\n              <span>Download Now</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FooterSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EACpD,oBACEH,OAAA;IAAKI,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BL,OAAA;MAAKI,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BL,OAAA;QAAII,SAAS,EAAC,OAAO;QAACE,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAF,QAAA,EAAC;MAEjD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELX,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BL,OAAA;UAAGI,SAAS,EAAC,WAAW;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAA2B,CAAE;UAAAF,QAAA,EAAC;QAEvE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJX,OAAA;UAAKI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BL,OAAA;YACEI,SAAS,EAAC,8BAA8B;YACxCQ,OAAO,EAAET,cAAe;YAAAE,QAAA,eAExBL,OAAA;cAAAK,QAAA,EAAM;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAzBIZ,WAAW;AA2BjB,eAAeA,WAAW;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}