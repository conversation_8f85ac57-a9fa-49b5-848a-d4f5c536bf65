{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/Header/Header';\nimport Navigation from './components/Navigation/Navigation';\nimport Homepage from './components/Homepage/Homepage';\nimport Footer from './components/Footer/Footer';\nimport Interface from './components/Interactive/Interface';\nimport AppDownloadPopup from './components/Interactive/AppDownloadPopup';\nimport CookieBanner from './components/Interactive/CookieBanner';\nimport VideoPlayer from './components/Interactive/VideoPlayer';\nimport './App.css';\nimport './styles/layout.css';\nimport './styles/homepage.css';\nimport './styles/interactive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isAppPopupOpen, setIsAppPopupOpen] = useState(false);\n  const [isCookieBannerVisible, setIsCookieBannerVisible] = useState(true);\n  const [isVideoPlayerOpen, setIsVideoPlayerOpen] = useState(false);\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => {\n      setIsLoaded(true);\n      document.body.classList.add('loaded');\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n  const openAppPopup = () => {\n    setIsAppPopupOpen(true);\n  };\n  const closeAppPopup = () => {\n    setIsAppPopupOpen(false);\n  };\n  const closeCookieBanner = () => {\n    setIsCookieBannerVisible(false);\n  };\n  const openVideoPlayer = videoUrl => {\n    setIsVideoPlayerOpen(true);\n  };\n  const closeVideoPlayer = () => {\n    setIsVideoPlayerOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(Interface, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {\n      onToggleMobileMenu: toggleMobileMenu,\n      onOpenAppPopup: openAppPopup,\n      isMobileMenuOpen: isMobileMenuOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n      isOpen: isMobileMenuOpen,\n      onClose: toggleMobileMenu,\n      onOpenAppPopup: openAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Homepage, {\n      onOpenAppPopup: openAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      onOpenAppPopup: openAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppDownloadPopup, {\n      isOpen: isAppPopupOpen,\n      onClose: closeAppPopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), isCookieBannerVisible && /*#__PURE__*/_jsxDEV(CookieBanner, {\n      onClose: closeCookieBanner\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(VideoPlayer, {\n      isOpen: isVideoPlayerOpen,\n      onClose: closeVideoPlayer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"audio\", {\n      id: \"background-audio\",\n      loop: true,\n      preload: \"none\",\n      children: /*#__PURE__*/_jsxDEV(\"source\", {\n        src: \"/media/araj.mp3\",\n        type: \"audio/mpeg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"NY8MAa8Oq3qpDHU4eyrvVUExhs4=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "Navigation", "Homepage", "Footer", "Interface", "AppDownloadPopup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VideoPlayer", "jsxDEV", "_jsxDEV", "App", "_s", "isLoaded", "setIsLoaded", "isMobileMenuOpen", "setIsMobileMenuOpen", "isAppPopupOpen", "setIsAppPopupOpen", "isCookieBannerVisible", "setIsCookieBannerVisible", "isVideoPlayerOpen", "setIsVideoPlayerOpen", "timer", "setTimeout", "document", "body", "classList", "add", "clearTimeout", "toggleMobileMenu", "openAppPopup", "closeAppPopup", "closeCookieBanner", "openVideoPlayer", "videoUrl", "closeVideoPlayer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onToggleMobileMenu", "onOpenAppPopup", "isOpen", "onClose", "id", "loop", "preload", "src", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Header from './components/Header/Header';\nimport Navigation from './components/Navigation/Navigation';\nimport Homepage from './components/Homepage/Homepage';\nimport Footer from './components/Footer/Footer';\nimport Interface from './components/Interactive/Interface';\nimport AppDownloadPopup from './components/Interactive/AppDownloadPopup';\nimport CookieBanner from './components/Interactive/CookieBanner';\nimport VideoPlayer from './components/Interactive/VideoPlayer';\nimport './App.css';\nimport './styles/layout.css';\nimport './styles/homepage.css';\nimport './styles/interactive.css';\n\nfunction App() {\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isAppPopupOpen, setIsAppPopupOpen] = useState(false);\n  const [isCookieBannerVisible, setIsCookieBannerVisible] = useState(true);\n  const [isVideoPlayerOpen, setIsVideoPlayerOpen] = useState(false);\n\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => {\n      setIsLoaded(true);\n      document.body.classList.add('loaded');\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  const openAppPopup = () => {\n    setIsAppPopupOpen(true);\n  };\n\n  const closeAppPopup = () => {\n    setIsAppPopupOpen(false);\n  };\n\n  const closeCookieBanner = () => {\n    setIsCookieBannerVisible(false);\n  };\n\n  const openVideoPlayer = (videoUrl) => {\n    setIsVideoPlayerOpen(true);\n  };\n\n  const closeVideoPlayer = () => {\n    setIsVideoPlayerOpen(false);\n  };\n\n  return (\n    <div className=\"App\">\n      <Interface />\n\n      <Header\n        onToggleMobileMenu={toggleMobileMenu}\n        onOpenAppPopup={openAppPopup}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n\n      <Navigation\n        isOpen={isMobileMenuOpen}\n        onClose={toggleMobileMenu}\n        onOpenAppPopup={openAppPopup}\n      />\n\n      <Homepage\n        onOpenAppPopup={openAppPopup}\n      />\n\n      <Footer onOpenAppPopup={openAppPopup} />\n\n      <AppDownloadPopup\n        isOpen={isAppPopupOpen}\n        onClose={closeAppPopup}\n      />\n\n      {isCookieBannerVisible && (\n        <CookieBanner onClose={closeCookieBanner} />\n      )}\n\n      <VideoPlayer\n        isOpen={isVideoPlayerOpen}\n        onClose={closeVideoPlayer}\n      />\n\n      <audio id=\"background-audio\" loop preload=\"none\">\n        <source src=\"/media/araj.mp3\" type=\"audio/mpeg\" />\n      </audio>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,OAAO,WAAW;AAClB,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAC9B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BV,WAAW,CAAC,IAAI,CAAC;MACjBW,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,YAAY,CAACN,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzBb,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1Bd,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bb,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;EAED,MAAMc,eAAe,GAAIC,QAAQ,IAAK;IACpCb,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,oBACEZ,OAAA;IAAK2B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB5B,OAAA,CAACL,SAAS;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEbhC,OAAA,CAACT,MAAM;MACL0C,kBAAkB,EAAEb,gBAAiB;MACrCc,cAAc,EAAEb,YAAa;MAC7BhB,gBAAgB,EAAEA;IAAiB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAEFhC,OAAA,CAACR,UAAU;MACT2C,MAAM,EAAE9B,gBAAiB;MACzB+B,OAAO,EAAEhB,gBAAiB;MAC1Bc,cAAc,EAAEb;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAEFhC,OAAA,CAACP,QAAQ;MACPyC,cAAc,EAAEb;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAEFhC,OAAA,CAACN,MAAM;MAACwC,cAAc,EAAEb;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExChC,OAAA,CAACJ,gBAAgB;MACfuC,MAAM,EAAE5B,cAAe;MACvB6B,OAAO,EAAEd;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,EAEDvB,qBAAqB,iBACpBT,OAAA,CAACH,YAAY;MAACuC,OAAO,EAAEb;IAAkB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5C,eAEDhC,OAAA,CAACF,WAAW;MACVqC,MAAM,EAAExB,iBAAkB;MAC1ByB,OAAO,EAAEV;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAEFhC,OAAA;MAAOqC,EAAE,EAAC,kBAAkB;MAACC,IAAI;MAACC,OAAO,EAAC,MAAM;MAAAX,QAAA,eAC9C5B,OAAA;QAAQwC,GAAG,EAAC,iBAAiB;QAACC,IAAI,EAAC;MAAY;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAC9B,EAAA,CAlFQD,GAAG;AAAAyC,EAAA,GAAHzC,GAAG;AAoFZ,eAAeA,GAAG;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}