{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\n// Initialize global loader and body opacity\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndocument.addEventListener('DOMContentLoaded', () => {\n  // Set initial body opacity\n  document.body.style.opacity = '0';\n\n  // Add loaded class after a short delay to trigger fade in\n  setTimeout(() => {\n    document.body.classList.add('loaded');\n    document.body.style.opacity = '1';\n  }, 500);\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 21,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "jsxDEV", "_jsxDEV", "document", "addEventListener", "body", "style", "opacity", "setTimeout", "classList", "add", "root", "createRoot", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\n// Initialize global loader and body opacity\ndocument.addEventListener('DOMContentLoaded', () => {\n  // Set initial body opacity\n  document.body.style.opacity = '0';\n\n  // Add loaded class after a short delay to trigger fade in\n  setTimeout(() => {\n    document.body.classList.add('loaded');\n    document.body.style.opacity = '1';\n  }, 500);\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;EAClD;EACAD,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,GAAG;;EAEjC;EACAC,UAAU,CAAC,MAAM;IACfL,QAAQ,CAACE,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IACrCP,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,GAAG;EACnC,CAAC,EAAE,GAAG,CAAC;AACT,CAAC,CAAC;AAEF,MAAMI,IAAI,GAAGb,QAAQ,CAACc,UAAU,CAACT,QAAQ,CAACU,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEF,IAAI,CAACG,MAAM,cACTZ,OAAA,CAACL,KAAK,CAACkB,UAAU;EAAAC,QAAA,eACfd,OAAA,CAACH,GAAG;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC;;AAED;AACA;AACA;AACApB,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}