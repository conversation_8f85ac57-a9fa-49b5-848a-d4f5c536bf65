{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Homepage/Homepage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport WelcomeSlide from '../Slides/WelcomeSlide';\nimport AwakenSlide from '../Slides/AwakenSlide';\nimport ReturnSlide from '../Slides/ReturnSlide';\nimport PossibleSlide from '../Slides/PossibleSlide';\nimport FooterSlide from '../Slides/FooterSlide';\nimport useSlideNavigation from '../../hooks/useSlideNavigation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Homepage = ({\n  onOpenAppPopup\n}) => {\n  _s();\n  const {\n    currentSlide,\n    goToSlide\n  } = useSlideNavigation(5);\n  console.log('Homepage rendering, currentSlide:', currentSlide);\n  const slides = [{\n    component: WelcomeSlide,\n    backgroundImage: '/images/slide1-bg.jpg',\n    backgroundPosition: 'center 35%'\n  }, {\n    component: AwakenSlide,\n    backgroundImage: '/images/slide2-bg.jpg',\n    backgroundPosition: 'center 80%'\n  }, {\n    component: ReturnSlide,\n    backgroundImage: '/images/slide3-bg.jpg',\n    backgroundPosition: 'center 35%'\n  }, {\n    component: PossibleSlide,\n    backgroundImage: '/images/slide4-bg.jpg',\n    backgroundPosition: 'center 75%'\n  }, {\n    component: FooterSlide,\n    backgroundImage: '/images/slide5-bg.jpg',\n    backgroundPosition: 'center',\n    backgroundColor: '#51301d'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homepage\",\n    children: [slides.map((slide, index) => {\n      const SlideComponent = slide.component;\n      const isActive = index === currentSlide;\n      const slideDirection = index < currentSlide ? 'pageslide-up' : 'pageslide-down';\n      console.log(`Slide ${index}: isActive=${isActive}, currentSlide=${currentSlide}`);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${!isActive ? slideDirection : ''}`,\n        \"data-gl-background\": slide.background,\n        style: {\n          zIndex: isActive ? 10 : 1,\n          opacity: isActive ? 1 : 0,\n          transform: isActive ? 'translateY(0)' : index < currentSlide ? 'translateY(-100vh)' : 'translateY(100vh)',\n          backgroundColor: index === 4 ? '#51301d' : 'transparent' // Footer slide background\n        },\n        children: /*#__PURE__*/_jsxDEV(SlideComponent, {\n          isActive: isActive,\n          onOpenAppPopup: onOpenAppPopup\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-indicators\",\n      children: slides.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `slide-indicator ${index === currentSlide ? 'active' : ''}`,\n        onClick: () => goToSlide(index),\n        title: `Go to slide ${index + 1}`\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Homepage, \"/qio3LyGzkG7vna9UDXAIH9YxJk=\", false, function () {\n  return [useSlideNavigation];\n});\n_c = Homepage;\nexport default Homepage;\nvar _c;\n$RefreshReg$(_c, \"Homepage\");", "map": {"version": 3, "names": ["React", "WelcomeSlide", "AwakenSlide", "ReturnSlide", "PossibleSlide", "FooterSlide", "useSlideNavigation", "jsxDEV", "_jsxDEV", "Homepage", "onOpenAppPopup", "_s", "currentSlide", "goToSlide", "console", "log", "slides", "component", "backgroundImage", "backgroundPosition", "backgroundColor", "className", "children", "map", "slide", "index", "SlideComponent", "isActive", "slideDirection", "background", "style", "zIndex", "opacity", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Homepage/Homepage.js"], "sourcesContent": ["import React from 'react';\nimport WelcomeSlide from '../Slides/WelcomeSlide';\nimport AwakenSlide from '../Slides/AwakenSlide';\nimport ReturnSlide from '../Slides/ReturnSlide';\nimport PossibleSlide from '../Slides/PossibleSlide';\nimport FooterSlide from '../Slides/FooterSlide';\nimport useSlideNavigation from '../../hooks/useSlideNavigation';\n\nconst Homepage = ({ onOpenAppPopup }) => {\n  const { currentSlide, goToSlide } = useSlideNavigation(5);\n\n  console.log('Homepage rendering, currentSlide:', currentSlide);\n\n  const slides = [\n    {\n      component: WelcomeSlide,\n      backgroundImage: '/images/slide1-bg.jpg',\n      backgroundPosition: 'center 35%'\n    },\n    {\n      component: AwakenSlide,\n      backgroundImage: '/images/slide2-bg.jpg',\n      backgroundPosition: 'center 80%'\n    },\n    {\n      component: ReturnSlide,\n      backgroundImage: '/images/slide3-bg.jpg',\n      backgroundPosition: 'center 35%'\n    },\n    {\n      component: PossibleSlide,\n      backgroundImage: '/images/slide4-bg.jpg',\n      backgroundPosition: 'center 75%'\n    },\n    {\n      component: FooterSlide,\n      backgroundImage: '/images/slide5-bg.jpg',\n      backgroundPosition: 'center',\n      backgroundColor: '#51301d'\n    }\n  ];\n\n  return (\n    <div className=\"homepage\">\n      {slides.map((slide, index) => {\n        const SlideComponent = slide.component;\n        const isActive = index === currentSlide;\n        const slideDirection = index < currentSlide ? 'pageslide-up' : 'pageslide-down';\n\n        console.log(`Slide ${index}: isActive=${isActive}, currentSlide=${currentSlide}`);\n\n        return (\n          <div\n            key={index}\n            className={`fullsize slide-${index + 1} pageslide ${isActive ? 'active' : ''} ${!isActive ? slideDirection : ''}`}\n            data-gl-background={slide.background}\n            style={{\n              zIndex: isActive ? 10 : 1,\n              opacity: isActive ? 1 : 0,\n              transform: isActive ? 'translateY(0)' :\n                        index < currentSlide ? 'translateY(-100vh)' : 'translateY(100vh)',\n              backgroundColor: index === 4 ? '#51301d' : 'transparent' // Footer slide background\n            }}\n          >\n            <SlideComponent\n              isActive={isActive}\n              onOpenAppPopup={onOpenAppPopup}\n            />\n          </div>\n        );\n      })}\n\n      {/* Slide Indicators */}\n      <div className=\"slide-indicators\">\n        {slides.map((_, index) => (\n          <div\n            key={index}\n            className={`slide-indicator ${index === currentSlide ? 'active' : ''}`}\n            onClick={() => goToSlide(index)}\n            title={`Go to slide ${index + 1}`}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default Homepage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,kBAAkB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,YAAY;IAAEC;EAAU,CAAC,GAAGP,kBAAkB,CAAC,CAAC,CAAC;EAEzDQ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,YAAY,CAAC;EAE9D,MAAMI,MAAM,GAAG,CACb;IACEC,SAAS,EAAEhB,YAAY;IACvBiB,eAAe,EAAE,uBAAuB;IACxCC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEF,SAAS,EAAEf,WAAW;IACtBgB,eAAe,EAAE,uBAAuB;IACxCC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEF,SAAS,EAAEd,WAAW;IACtBe,eAAe,EAAE,uBAAuB;IACxCC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEF,SAAS,EAAEb,aAAa;IACxBc,eAAe,EAAE,uBAAuB;IACxCC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEF,SAAS,EAAEZ,WAAW;IACtBa,eAAe,EAAE,uBAAuB;IACxCC,kBAAkB,EAAE,QAAQ;IAC5BC,eAAe,EAAE;EACnB,CAAC,CACF;EAED,oBACEZ,OAAA;IAAKa,SAAS,EAAC,UAAU;IAAAC,QAAA,GACtBN,MAAM,CAACO,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MAC5B,MAAMC,cAAc,GAAGF,KAAK,CAACP,SAAS;MACtC,MAAMU,QAAQ,GAAGF,KAAK,KAAKb,YAAY;MACvC,MAAMgB,cAAc,GAAGH,KAAK,GAAGb,YAAY,GAAG,cAAc,GAAG,gBAAgB;MAE/EE,OAAO,CAACC,GAAG,CAAC,SAASU,KAAK,cAAcE,QAAQ,kBAAkBf,YAAY,EAAE,CAAC;MAEjF,oBACEJ,OAAA;QAEEa,SAAS,EAAE,kBAAkBI,KAAK,GAAG,CAAC,cAAcE,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAI,CAACA,QAAQ,GAAGC,cAAc,GAAG,EAAE,EAAG;QAClH,sBAAoBJ,KAAK,CAACK,UAAW;QACrCC,KAAK,EAAE;UACLC,MAAM,EAAEJ,QAAQ,GAAG,EAAE,GAAG,CAAC;UACzBK,OAAO,EAAEL,QAAQ,GAAG,CAAC,GAAG,CAAC;UACzBM,SAAS,EAAEN,QAAQ,GAAG,eAAe,GAC3BF,KAAK,GAAGb,YAAY,GAAG,oBAAoB,GAAG,mBAAmB;UAC3EQ,eAAe,EAAEK,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,aAAa,CAAC;QAC3D,CAAE;QAAAH,QAAA,eAEFd,OAAA,CAACkB,cAAc;UACbC,QAAQ,EAAEA,QAAS;UACnBjB,cAAc,EAAEA;QAAe;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC,GAdGZ,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeP,CAAC;IAEV,CAAC,CAAC,eAGF7B,OAAA;MAAKa,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BN,MAAM,CAACO,GAAG,CAAC,CAACe,CAAC,EAAEb,KAAK,kBACnBjB,OAAA;QAEEa,SAAS,EAAE,mBAAmBI,KAAK,KAAKb,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;QACvE2B,OAAO,EAAEA,CAAA,KAAM1B,SAAS,CAACY,KAAK,CAAE;QAChCe,KAAK,EAAE,eAAef,KAAK,GAAG,CAAC;MAAG,GAH7BA,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA7EIF,QAAQ;EAAA,QACwBH,kBAAkB;AAAA;AAAAmC,EAAA,GADlDhC,QAAQ;AA+Ed,eAAeA,QAAQ;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}