{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/ReturnSlide.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReturnSlide = ({\n  isActive\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout center-h\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slide-inner\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"fs-h1\",\n        children: \"Return to your glorious past\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-labeur\",\n          children: \"Discover perfect inner balance and harmony with the path of the Vedas.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/?page_id=718\",\n          target: \"\",\n          className: \"link\",\n          children: [\"READ MORE\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"underline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ReturnSlide;\nexport default ReturnSlide;\nvar _c;\n$RefreshReg$(_c, \"ReturnSlide\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ReturnSlide", "isActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Slides/ReturnSlide.js"], "sourcesContent": ["import React from 'react';\n\nconst ReturnSlide = ({ isActive }) => {\n  return (\n    <div className=\"layout center-h\">\n      <div className=\"slide-inner\">\n        <h1 className=\"fs-h1\">\n          Return to your glorious past\n        </h1>\n\n        <div className=\"text-wrapper\">\n          <p className=\"fs-labeur\">\n            Discover perfect inner balance and harmony with the path of the Vedas.\n          </p>\n\n          <a\n            href=\"/?page_id=718\"\n            target=\"\"\n            className=\"link\"\n          >\n            READ MORE\n            <span className=\"underline\"></span>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReturnSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACpC,oBACEF,OAAA;IAAKG,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BJ,OAAA;MAAKG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BJ,OAAA;QAAIG,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAC;MAEtB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELR,OAAA;QAAKG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BJ,OAAA;UAAGG,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA;UACES,IAAI,EAAC,eAAe;UACpBC,MAAM,EAAC,EAAE;UACTP,SAAS,EAAC,MAAM;UAAAC,QAAA,GACjB,WAEC,eAAAJ,OAAA;YAAMG,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAzBIV,WAAW;AA2BjB,eAAeA,WAAW;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}