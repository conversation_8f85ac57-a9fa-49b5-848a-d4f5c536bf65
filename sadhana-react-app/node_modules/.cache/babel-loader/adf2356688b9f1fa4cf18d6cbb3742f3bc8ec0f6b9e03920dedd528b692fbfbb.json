{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/CookieBanner.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CookieBanner = ({\n  onClose\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(false);\n  useEffect(() => {\n    // Check if user has already accepted/declined cookies\n    const cookieConsent = localStorage.getItem('cookieConsent');\n    if (!cookieConsent) {\n      // Show banner after a short delay\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n      }, 2000);\n      return () => clearTimeout(timer);\n    } else {\n      onClose();\n    }\n  }, [onClose]);\n  const handleAccept = () => {\n    localStorage.setItem('cookieConsent', 'accepted');\n    setIsVisible(false);\n    onClose();\n  };\n  const handleDecline = () => {\n    localStorage.setItem('cookieConsent', 'declined');\n    setIsVisible(false);\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"cookie-banner\",\n    className: isVisible ? 'active' : '',\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"We use cookies to personalize your site experience:\", ' ', /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/privacy-policy/\",\n          children: \"read more\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button button-accept no-icon brown\",\n        onClick: handleAccept,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/hover-dark.png\",\n          className: \"picture-hover\",\n          alt: \"\",\n          loading: \"lazy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Accept\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button button-decline no-icon transparent\",\n        onClick: handleDecline,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/hover-light.png\",\n          className: \"picture-hover\",\n          alt: \"\",\n          loading: \"lazy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Decline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(CookieBanner, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c = CookieBanner;\nexport default CookieBanner;\nvar _c;\n$RefreshReg$(_c, \"CookieBanner\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClose", "_s", "isVisible", "setIsVisible", "cookieConsent", "localStorage", "getItem", "timer", "setTimeout", "clearTimeout", "handleAccept", "setItem", "handleDecline", "id", "className", "children", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "alt", "loading", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Interactive/CookieBanner.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst CookieBanner = ({ onClose }) => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Check if user has already accepted/declined cookies\n    const cookieConsent = localStorage.getItem('cookieConsent');\n    if (!cookieConsent) {\n      // Show banner after a short delay\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n      }, 2000);\n      return () => clearTimeout(timer);\n    } else {\n      onClose();\n    }\n  }, [onClose]);\n\n  const handleAccept = () => {\n    localStorage.setItem('cookieConsent', 'accepted');\n    setIsVisible(false);\n    onClose();\n  };\n\n  const handleDecline = () => {\n    localStorage.setItem('cookieConsent', 'declined');\n    setIsVisible(false);\n    onClose();\n  };\n\n  return (\n    <div \n      id=\"cookie-banner\" \n      className={isVisible ? 'active' : ''}\n    >\n      <div className=\"content\">\n        <span>\n          We use cookies to personalize your site experience:{' '}\n          <a href=\"/privacy-policy/\">read more</a>\n        </span>\n        \n        <button \n          className=\"button button-accept no-icon brown\"\n          onClick={handleAccept}\n        >\n          <img \n            src=\"/images/hover-dark.png\" \n            className=\"picture-hover\" \n            alt=\"\" \n            loading=\"lazy\" \n          />\n          <span>Accept</span>\n        </button>\n        \n        <button \n          className=\"button button-decline no-icon transparent\"\n          onClick={handleDecline}\n        >\n          <img \n            src=\"/images/hover-light.png\" \n            className=\"picture-hover\" \n            alt=\"\" \n            loading=\"lazy\" \n          />\n          <span>Decline</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CookieBanner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMQ,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC3D,IAAI,CAACF,aAAa,EAAE;MAClB;MACA,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BL,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMM,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLP,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBL,YAAY,CAACM,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;IACjDR,YAAY,CAAC,KAAK,CAAC;IACnBH,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1BP,YAAY,CAACM,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;IACjDR,YAAY,CAAC,KAAK,CAAC;IACnBH,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACEF,OAAA;IACEe,EAAE,EAAC,eAAe;IAClBC,SAAS,EAAEZ,SAAS,GAAG,QAAQ,GAAG,EAAG;IAAAa,QAAA,eAErCjB,OAAA;MAAKgB,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBjB,OAAA;QAAAiB,QAAA,GAAM,qDAC+C,EAAC,GAAG,eACvDjB,OAAA;UAAGkB,IAAI,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAEPtB,OAAA;QACEgB,SAAS,EAAC,oCAAoC;QAC9CO,OAAO,EAAEX,YAAa;QAAAK,QAAA,gBAEtBjB,OAAA;UACEwB,GAAG,EAAC,wBAAwB;UAC5BR,SAAS,EAAC,eAAe;UACzBS,GAAG,EAAC,EAAE;UACNC,OAAO,EAAC;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFtB,OAAA;UAAAiB,QAAA,EAAM;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAETtB,OAAA;QACEgB,SAAS,EAAC,2CAA2C;QACrDO,OAAO,EAAET,aAAc;QAAAG,QAAA,gBAEvBjB,OAAA;UACEwB,GAAG,EAAC,yBAAyB;UAC7BR,SAAS,EAAC,eAAe;UACzBS,GAAG,EAAC,EAAE;UACNC,OAAO,EAAC;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFtB,OAAA;UAAAiB,QAAA,EAAM;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CApEIF,YAAY;AAAA0B,EAAA,GAAZ1B,YAAY;AAsElB,eAAeA,YAAY;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}