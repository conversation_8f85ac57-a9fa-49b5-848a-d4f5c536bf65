{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Footer/Footer.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = ({\n  onOpenAppPopup\n}) => {\n  const footerLinks = [{\n    href: \"/terms\",\n    text: \"Terms of Service\",\n    target: \"\"\n  }, {\n    href: \"/privacy\",\n    text: \"Privacy Policy\",\n    target: \"\"\n  }, {\n    href: \"/warranty\",\n    text: \"Lifetime Warranty\",\n    target: \"\"\n  }, {\n    href: \"/care-guide\",\n    text: \"Jewelry Care\",\n    target: \"\"\n  }, {\n    href: \"/about\",\n    text: \"About Us\",\n    target: \"\"\n  }];\n  const socialLinks = [{\n    href: \"https://twitter.com/DiamondAtelier\",\n    icon: \"icon-twitter\",\n    target: \"_blank\"\n  }, {\n    href: \"https://www.instagram.com/diamond_atelier_official/\",\n    icon: \"icon-instagram\",\n    target: \"_blank\"\n  }, {\n    href: \"https://www.facebook.com/DiamondAtelier\",\n    icon: \"icon-facebook\",\n    target: \"_blank\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"footer-wrapper\",\n    className: \"page-size-scrolling-unit\",\n    children: /*#__PURE__*/_jsxDEV(\"footer\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"emblem\",\n          src: \"/images/diamond-logo-white.svg\",\n          alt: \"Diamond Atelier\",\n          width: \"180\",\n          height: \"120\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Visit Our Showroom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"123 Diamond District\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 38\n              }, this), \"New York, NY 10036\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 62\n              }, this), \"United States\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Phone: +1 (555) 123-GEMS\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 42\n              }, this), \"Email: <EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 78\n              }, this), \"Hours: Mon-Sat 10AM-7PM\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"services-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Custom Design\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Bespoke jewelry crafted to your vision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Expert Consultation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Professional guidance for your perfect piece\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"service-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Lifetime Warranty\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Comprehensive coverage for all our creations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"bottom-links\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '650px'\n          },\n          children: [footerLinks.map((link, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: link.href,\n              target: link.target,\n              children: link.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"social hide-mobile\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Follow us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: social.href,\n              target: social.target,\n              rel: \"noopener noreferrer\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `icon ${social.icon}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "onOpenAppPopup", "footerLinks", "href", "text", "target", "socialLinks", "icon", "id", "className", "children", "src", "alt", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "max<PERSON><PERSON><PERSON>", "map", "link", "index", "social", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/saveweb2zip-com-sadhana-app/sadhana-react-app/src/components/Footer/Footer.js"], "sourcesContent": ["import React from 'react';\n\nconst Footer = ({ onOpenAppPopup }) => {\n  const footerLinks = [\n    { href: \"/terms\", text: \"Terms of Service\", target: \"\" },\n    { href: \"/privacy\", text: \"Privacy Policy\", target: \"\" },\n    { href: \"/warranty\", text: \"Lifetime Warranty\", target: \"\" },\n    { href: \"/care-guide\", text: \"Jewelry Care\", target: \"\" },\n    { href: \"/about\", text: \"About Us\", target: \"\" }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/DiamondAtelier\",\n      icon: \"icon-twitter\",\n      target: \"_blank\"\n    },\n    {\n      href: \"https://www.instagram.com/diamond_atelier_official/\",\n      icon: \"icon-instagram\",\n      target: \"_blank\"\n    },\n    {\n      href: \"https://www.facebook.com/DiamondAtelier\",\n      icon: \"icon-facebook\",\n      target: \"_blank\"\n    }\n  ];\n\n  return (\n    <div id=\"footer-wrapper\" className=\"page-size-scrolling-unit\">\n      <footer>\n        <div className=\"content\">\n          <img\n            className=\"emblem\"\n            src=\"/images/diamond-logo-white.svg\"\n            alt=\"Diamond Atelier\"\n            width=\"180\"\n            height=\"120\"\n          />\n\n          <div className=\"contact-info\">\n            <div className=\"contact-section\">\n              <h3>Visit Our Showroom</h3>\n              <p>123 Diamond District<br />New York, NY 10036<br />United States</p>\n            </div>\n\n            <div className=\"contact-section\">\n              <h3>Contact Us</h3>\n              <p>Phone: +1 (555) 123-GEMS<br />Email: <EMAIL><br />Hours: Mon-Sat 10AM-7PM</p>\n            </div>\n          </div>\n\n          <div className=\"services-info\">\n            <div className=\"service-item\">\n              <h4>Custom Design</h4>\n              <p>Bespoke jewelry crafted to your vision</p>\n            </div>\n\n            <div className=\"service-item\">\n              <h4>Expert Consultation</h4>\n              <p>Professional guidance for your perfect piece</p>\n            </div>\n\n            <div className=\"service-item\">\n              <h4>Lifetime Warranty</h4>\n              <p>Comprehensive coverage for all our creations</p>\n            </div>\n          </div>\n        </div>\n\n        <ul className=\"bottom-links\">\n          <div style={{ maxWidth: '650px' }}>\n            {footerLinks.map((link, index) => (\n              <li key={index}>\n                <a href={link.href} target={link.target}>\n                  {link.text}\n                </a>\n              </li>\n            ))}\n            \n            <li className=\"social hide-mobile\">\n              <span>Follow us</span>\n              {socialLinks.map((social, index) => (\n                <a \n                  key={index}\n                  href={social.href} \n                  target={social.target}\n                  rel=\"noopener noreferrer\"\n                >\n                  <i className={`icon ${social.icon}`}></i>\n                </a>\n              ))}\n            </li>\n          </div>\n        </ul>\n      </footer>\n    </div>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAe,CAAC,KAAK;EACrC,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,MAAM,EAAE;EAAG,CAAC,EACxD;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,MAAM,EAAE;EAAG,CAAC,EACxD;IAAEF,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC5D;IAAEF,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,cAAc;IAAEC,MAAM,EAAE;EAAG,CAAC,EACzD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,UAAU;IAAEC,MAAM,EAAE;EAAG,CAAC,CACjD;EAED,MAAMC,WAAW,GAAG,CAClB;IACEH,IAAI,EAAE,oCAAoC;IAC1CI,IAAI,EAAE,cAAc;IACpBF,MAAM,EAAE;EACV,CAAC,EACD;IACEF,IAAI,EAAE,qDAAqD;IAC3DI,IAAI,EAAE,gBAAgB;IACtBF,MAAM,EAAE;EACV,CAAC,EACD;IACEF,IAAI,EAAE,yCAAyC;IAC/CI,IAAI,EAAE,eAAe;IACrBF,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEN,OAAA;IAAKS,EAAE,EAAC,gBAAgB;IAACC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,eAC3DX,OAAA;MAAAW,QAAA,gBACEX,OAAA;QAAKU,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBX,OAAA;UACEU,SAAS,EAAC,QAAQ;UAClBE,GAAG,EAAC,gCAAgC;UACpCC,GAAG,EAAC,iBAAiB;UACrBC,KAAK,EAAC,KAAK;UACXC,MAAM,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEFnB,OAAA;UAAKU,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BX,OAAA;YAAKU,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BX,OAAA;cAAAW,QAAA,EAAI;YAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BnB,OAAA;cAAAW,QAAA,GAAG,sBAAoB,eAAAX,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,sBAAkB,eAAAnB,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,iBAAa;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAENnB,OAAA;YAAKU,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BX,OAAA;cAAAW,QAAA,EAAI;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBnB,OAAA;cAAAW,QAAA,GAAG,0BAAwB,eAAAX,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,kCAA8B,eAAAnB,OAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,2BAAuB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAKU,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BX,OAAA;YAAKU,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BX,OAAA;cAAAW,QAAA,EAAI;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBnB,OAAA;cAAAW,QAAA,EAAG;YAAsC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAENnB,OAAA;YAAKU,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BX,OAAA;cAAAW,QAAA,EAAI;YAAmB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BnB,OAAA;cAAAW,QAAA,EAAG;YAA4C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eAENnB,OAAA;YAAKU,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BX,OAAA;cAAAW,QAAA,EAAI;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BnB,OAAA;cAAAW,QAAA,EAAG;YAA4C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAIU,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC1BX,OAAA;UAAKoB,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ,CAAE;UAAAV,QAAA,GAC/BR,WAAW,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BxB,OAAA;YAAAW,QAAA,eACEX,OAAA;cAAGI,IAAI,EAAEmB,IAAI,CAACnB,IAAK;cAACE,MAAM,EAAEiB,IAAI,CAACjB,MAAO;cAAAK,QAAA,EACrCY,IAAI,CAAClB;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC,GAHGK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACL,CAAC,eAEFnB,OAAA;YAAIU,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAChCX,OAAA;cAAAW,QAAA,EAAM;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrBZ,WAAW,CAACe,GAAG,CAAC,CAACG,MAAM,EAAED,KAAK,kBAC7BxB,OAAA;cAEEI,IAAI,EAAEqB,MAAM,CAACrB,IAAK;cAClBE,MAAM,EAAEmB,MAAM,CAACnB,MAAO;cACtBoB,GAAG,EAAC,qBAAqB;cAAAf,QAAA,eAEzBX,OAAA;gBAAGU,SAAS,EAAE,QAAQe,MAAM,CAACjB,IAAI;cAAG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC,GALpCK,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMT,CACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACQ,EAAA,GAjGI1B,MAAM;AAmGZ,eAAeA,MAAM;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}